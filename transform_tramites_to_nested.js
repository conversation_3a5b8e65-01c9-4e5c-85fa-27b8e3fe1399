const fs = require('fs');

console.log('=== TRANSFORMING TRAMITES TO NESTED HIERARCHICAL STRUCTURE ===\n');

try {
  console.log('Loading required files...');
  
  // Load tramites data (flat array)
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  console.log(`✅ Tramites loaded: ${tramitesData.length} records`);
  
  // Load OPA metadata (hierarchical structure)
  const opaMetadata = JSON.parse(fs.readFileSync('opa_metadata.json', 'utf8'));
  console.log(`✅ OPA metadata loaded: ${Object.keys(opaMetadata.dependencias).length} dependencias`);
  
  console.log('\nStarting transformation...\n');
  
  // Initialize the nested structure
  const nestedTramites = {
    dependencias: {}
  };
  
  // Process each tramite and organize into nested structure
  tramitesData.forEach((tramite, index) => {
    const depCode = tramite.codigo_dependencia;
    const subCode = tramite.codigo_subdependencia;
    
    console.log(`Processing tramite ${index + 1}/${tramitesData.length}: ${tramite.codigo_unico}`);
    
    // Initialize dependencia if it doesn't exist
    if (!nestedTramites.dependencias[depCode]) {
      const depMetadata = opaMetadata.dependencias[depCode];
      if (!depMetadata) {
        console.warn(`⚠️  Warning: No metadata found for dependencia ${depCode}`);
        // Use tramite data as fallback
        nestedTramites.dependencias[depCode] = {
          nombre: tramite.dependencia,
          sigla: tramite.dependencia.split(' ').map(word => word[0]).join('').toUpperCase(),
          subdependencias: {}
        };
      } else {
        nestedTramites.dependencias[depCode] = {
          nombre: depMetadata.nombre,
          sigla: depMetadata.sigla,
          subdependencias: {}
        };
      }
    }
    
    // Initialize subdependencia if it doesn't exist
    if (!nestedTramites.dependencias[depCode].subdependencias[subCode]) {
      const subMetadata = opaMetadata.dependencias[depCode]?.subdependencias[subCode];
      if (!subMetadata) {
        console.warn(`⚠️  Warning: No metadata found for subdependencia ${depCode}-${subCode}`);
        // Use tramite data as fallback
        nestedTramites.dependencias[depCode].subdependencias[subCode] = {
          nombre: tramite.subdependencia,
          sigla: tramite.subdependencia.split(' ').map(word => word[0]).join('').toUpperCase(),
          tramites: []
        };
      } else {
        nestedTramites.dependencias[depCode].subdependencias[subCode] = {
          nombre: subMetadata.nombre,
          sigla: subMetadata.sigla,
          tramites: []
        };
      }
    }
    
    // Add tramite to the appropriate subdependencia
    const tramiteRecord = {
      codigo_unico: tramite.codigo_unico,
      nombre: tramite.Nombre,
      formulario: tramite.Formulario,
      tiempo_respuesta: tramite["Tiempo de respuesta"],
      tiene_pago: tramite["¿Tiene pago?"],
      visualizacion_suit: tramite["Visualización trámite en el SUIT"],
      visualizacion_gov: tramite["Visualización trámite en GOV.CO"]
    };
    
    nestedTramites.dependencias[depCode].subdependencias[subCode].tramites.push(tramiteRecord);
  });
  
  console.log('\n=== TRANSFORMATION SUMMARY ===');
  
  let totalTramitesProcessed = 0;
  Object.entries(nestedTramites.dependencias).forEach(([depCode, depData]) => {
    const depTramiteCount = Object.values(depData.subdependencias)
      .reduce((sum, subData) => sum + subData.tramites.length, 0);
    
    console.log(`${depCode}: ${depData.nombre} - ${depTramiteCount} tramites`);
    
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      console.log(`  └─ ${subCode}: ${subData.nombre} - ${subData.tramites.length} tramites`);
    });
    
    totalTramitesProcessed += depTramiteCount;
  });
  
  console.log(`\nTotal tramites processed: ${totalTramitesProcessed}`);
  console.log(`Original tramites count: ${tramitesData.length}`);
  
  if (totalTramitesProcessed === tramitesData.length) {
    console.log('✅ All tramites successfully transformed');
  } else {
    console.log('❌ Mismatch in tramite counts - review transformation');
    process.exit(1);
  }
  
  // Save the transformed structure
  console.log('\nSaving transformed structure...');
  fs.writeFileSync('tramites_chia_optimo.json', JSON.stringify(nestedTramites, null, 2));
  console.log('✅ Transformed tramites saved to tramites_chia_optimo.json');
  
  // Save transformation report
  const transformationReport = {
    timestamp: new Date().toISOString(),
    transformation: 'flat_array_to_nested_hierarchy',
    original_count: tramitesData.length,
    transformed_count: totalTramitesProcessed,
    dependencias_count: Object.keys(nestedTramites.dependencias).length,
    subdependencias_count: Object.values(nestedTramites.dependencias)
      .reduce((sum, dep) => sum + Object.keys(dep.subdependencias).length, 0),
    success: totalTramitesProcessed === tramitesData.length
  };
  
  fs.writeFileSync('tramites_transformation_report.json', JSON.stringify(transformationReport, null, 2));
  console.log('✅ Transformation report saved');
  
  console.log('\n🎉 TRANSFORMATION COMPLETED SUCCESSFULLY!');
  
} catch (error) {
  console.error('❌ Error during transformation:', error.message);
  console.error(error.stack);
  process.exit(1);
}
