const fs = require('fs');

console.log('=== STANDARDIZING OPA CODES ===\n');

try {
  console.log('Reading OPA file...');
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  console.log('✅ OPA file loaded successfully\n');

  let totalProcessed = 0;
  let totalDependencias = 0;
  let totalSubdependencias = 0;
  const processedCodes = new Set();
  const codeMapping = [];

  console.log('Processing OPA codes...\n');

  // Process each dependencia
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    totalDependencias++;
    console.log(`Processing DEPENDENCIA ${depCode}: ${depData.nombre} (${depData.sigla})`);
    
    // Process each subdependencia
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      totalSubdependencias++;
      const opaCount = subData.OPA ? subData.OPA.length : 0;
      
      console.log(`  └─ SUBDEPENDENCIA ${subCode}: ${subData.nombre} (${subData.sigla}) - ${opaCount} OPAs`);
      
      if (subData.OPA && subData.OPA.length > 0) {
        // Process each OPA in this subdependencia
        subData.OPA.forEach((opa, index) => {
          const oldCode = opa.codigo_OPA;
          const newCode = `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${(index + 1).toString().padStart(3, '0')}`;
          
          // Check for duplicates (should not happen with this logic, but safety check)
          if (processedCodes.has(newCode)) {
            console.log(`❌ WARNING: Duplicate code detected: ${newCode}`);
          } else {
            processedCodes.add(newCode);
          }
          
          // Update the codigo_OPA field
          opa.codigo_OPA = newCode;
          totalProcessed++;
          
          // Store mapping for documentation
          codeMapping.push({
            dependencia: `${depCode} - ${depData.nombre}`,
            subdependencia: `${subCode} - ${subData.nombre}`,
            oldCode: oldCode,
            newCode: newCode,
            opaText: opa.OPA.substring(0, 50) + (opa.OPA.length > 50 ? '...' : '')
          });
        });
        
        // Show sample of new codes
        const firstNew = subData.OPA[0].codigo_OPA;
        const lastNew = subData.OPA[subData.OPA.length - 1].codigo_OPA;
        console.log(`      ✅ Updated codes: ${firstNew} to ${lastNew}`);
      }
    });
    console.log('');
  });

  console.log(`=== PROCESSING SUMMARY ===`);
  console.log(`Total Dependencias processed: ${totalDependencias}`);
  console.log(`Total Subdependencias processed: ${totalSubdependencias}`);
  console.log(`Total OPAs processed: ${totalProcessed}`);
  console.log(`Unique codes generated: ${processedCodes.size}`);

  // Validation check
  if (totalProcessed === processedCodes.size) {
    console.log('✅ All codes are unique - validation passed');
  } else {
    console.log('❌ Code uniqueness validation failed');
    process.exit(1);
  }

  // Save the updated OPA file
  console.log('\nSaving updated OPA file...');
  fs.writeFileSync('OPA-chia-optimo.json', JSON.stringify(opaData, null, 2));
  console.log('✅ Updated OPA file saved');

  // Save code mapping for documentation
  console.log('Saving code mapping documentation...');
  const mappingDoc = {
    timestamp: new Date().toISOString(),
    summary: {
      totalDependencias: totalDependencias,
      totalSubdependencias: totalSubdependencias,
      totalOPAs: totalProcessed,
      uniqueCodes: processedCodes.size
    },
    codeMapping: codeMapping
  };
  
  fs.writeFileSync('opa_code_mapping.json', JSON.stringify(mappingDoc, null, 2));
  console.log('✅ Code mapping saved to opa_code_mapping.json');

  // Show sample of transformations
  console.log('\n=== SAMPLE TRANSFORMATIONS ===');
  codeMapping.slice(0, 10).forEach(mapping => {
    console.log(`${mapping.oldCode} → ${mapping.newCode} | ${mapping.opaText}`);
  });

  console.log('\n✅ OPA CODE STANDARDIZATION COMPLETE');
  console.log(`✅ ${totalProcessed} OPA codes successfully standardized to XXX-YYY-ZZZ format`);

} catch (error) {
  console.error('❌ Error during standardization:', error.message);
  console.error(error.stack);
  process.exit(1);
}
