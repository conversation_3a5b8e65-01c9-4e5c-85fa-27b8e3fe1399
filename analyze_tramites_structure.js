const fs = require('fs');

console.log('=== ANALYZING CURRENT TRAMITES STRUCTURE ===\n');

try {
  console.log('Reading tramites file...');
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  console.log(`✅ Tramites file loaded successfully - ${tramitesData.length} records\n`);

  // Analyze current structure
  let totalTramites = tramitesData.length;
  const dependenciaGroups = {};
  const codeAnalysis = {
    withCodes: 0,
    withoutCodes: 0,
    uniqueCodes: new Set(),
    duplicateCodes: []
  };

  console.log('CURRENT STRUCTURE ANALYSIS:\n');

  // Group by dependencia and subdependencia
  tramitesData.forEach((tramite, index) => {
    const depCode = tramite.codigo_dependencia;
    const subCode = tramite.codigo_subdependencia;
    const uniqueCode = tramite.codigo_unico;
    const depName = tramite.dependencia;
    const subName = tramite.subdependencia;

    // Code analysis
    if (uniqueCode) {
      codeAnalysis.withCodes++;
      if (codeAnalysis.uniqueCodes.has(uniqueCode)) {
        codeAnalysis.duplicateCodes.push(uniqueCode);
      } else {
        codeAnalysis.uniqueCodes.add(uniqueCode);
      }
    } else {
      codeAnalysis.withoutCodes++;
    }

    // Group analysis
    if (!dependenciaGroups[depCode]) {
      dependenciaGroups[depCode] = {
        nombre: depName,
        codigo: depCode,
        subdependencias: {},
        totalTramites: 0
      };
    }

    if (!dependenciaGroups[depCode].subdependencias[subCode]) {
      dependenciaGroups[depCode].subdependencias[subCode] = {
        nombre: subName,
        codigo: subCode,
        tramites: [],
        count: 0
      };
    }

    dependenciaGroups[depCode].subdependencias[subCode].tramites.push({
      index: index,
      nombre: tramite.Nombre,
      codigo_unico: uniqueCode,
      formulario: tramite.Formulario,
      tiempo_respuesta: tramite["Tiempo de respuesta"],
      tiene_pago: tramite["¿Tiene pago?"]
    });

    dependenciaGroups[depCode].subdependencias[subCode].count++;
    dependenciaGroups[depCode].totalTramites++;
  });

  // Display analysis
  console.log('DEPENDENCIA DISTRIBUTION:\n');
  Object.entries(dependenciaGroups).forEach(([depCode, depData]) => {
    console.log(`${depCode}: ${depData.nombre} - ${depData.totalTramites} tramites`);
    
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      console.log(`  └─ ${subCode}: ${subData.nombre} - ${subData.count} tramites`);
      
      // Show first few codes as examples
      const sampleCodes = subData.tramites.slice(0, 3).map(t => t.codigo_unico);
      console.log(`      Codes: [${sampleCodes.join(', ')}${subData.count > 3 ? ', ...' : ''}]`);
    });
    console.log('');
  });

  console.log(`=== SUMMARY ===`);
  console.log(`Total Tramites: ${totalTramites}`);
  console.log(`Total Dependencias: ${Object.keys(dependenciaGroups).length}`);
  console.log(`Total Subdependencias: ${Object.values(dependenciaGroups).reduce((sum, dep) => sum + Object.keys(dep.subdependencias).length, 0)}`);
  console.log(`Tramites with codes: ${codeAnalysis.withCodes}`);
  console.log(`Tramites without codes: ${codeAnalysis.withoutCodes}`);
  console.log(`Unique codes: ${codeAnalysis.uniqueCodes.size}`);
  console.log(`Duplicate codes: ${codeAnalysis.duplicateCodes.length}`);

  if (codeAnalysis.duplicateCodes.length > 0) {
    console.log(`\n❌ DUPLICATE CODES FOUND:`);
    codeAnalysis.duplicateCodes.forEach(code => console.log(`  ${code}`));
  } else {
    console.log(`\n✅ All codes are unique`);
  }

  // Check code format
  console.log(`\n=== CODE FORMAT ANALYSIS ===`);
  const codeFormatRegex = /^\d{3}-\d{3}-\d{3}$/;
  let validFormat = 0;
  let invalidFormat = 0;

  Array.from(codeAnalysis.uniqueCodes).forEach(code => {
    if (codeFormatRegex.test(code)) {
      validFormat++;
    } else {
      invalidFormat++;
      console.log(`Invalid format: ${code}`);
    }
  });

  console.log(`Valid format codes: ${validFormat}`);
  console.log(`Invalid format codes: ${invalidFormat}`);

  // Save analysis
  const analysisResult = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTramites: totalTramites,
      totalDependencias: Object.keys(dependenciaGroups).length,
      totalSubdependencias: Object.values(dependenciaGroups).reduce((sum, dep) => sum + Object.keys(dep.subdependencias).length, 0),
      codesWithFormat: codeAnalysis.withCodes,
      uniqueCodes: codeAnalysis.uniqueCodes.size,
      duplicateCodes: codeAnalysis.duplicateCodes.length,
      validFormatCodes: validFormat,
      invalidFormatCodes: invalidFormat
    },
    dependenciaGroups: dependenciaGroups,
    readyForTransformation: codeAnalysis.duplicateCodes.length === 0 && invalidFormat === 0
  };

  fs.writeFileSync('tramites_structure_analysis.json', JSON.stringify(analysisResult, null, 2));
  console.log('\n✅ Analysis saved to tramites_structure_analysis.json');

  if (analysisResult.readyForTransformation) {
    console.log('✅ Ready for hierarchical transformation');
  } else {
    console.log('❌ Issues found - review before transformation');
  }

} catch (error) {
  console.error('❌ Error during analysis:', error.message);
  process.exit(1);
}
