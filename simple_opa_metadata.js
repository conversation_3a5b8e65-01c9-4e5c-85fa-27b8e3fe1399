const fs = require('fs');

console.log('Reading OPA file for metadata...');

try {
  const opaContent = fs.readFileSync('OPA-chia-optimo.json', 'utf8');
  console.log('File read successfully, parsing JSON...');
  
  const opaData = JSON.parse(opaContent);
  console.log('JSON parsed successfully');
  
  const metadata = { dependencias: {} };
  
  console.log('Extracting structure...');
  
  for (const [depCode, depData] of Object.entries(opaData.dependencias)) {
    console.log(`${depCode}: ${depData.nombre}`);
    
    metadata.dependencias[depCode] = {
      nombre: depData.nombre,
      sigla: depData.sigla,
      subdependencias: {}
    };
    
    for (const [subCode, subData] of Object.entries(depData.subdependencias)) {
      metadata.dependencias[depCode].subdependencias[subCode] = {
        nombre: subData.nombre,
        sigla: subData.sigla
      };
    }
  }
  
  fs.writeFileSync('opa_metadata.json', JSON.stringify(metadata, null, 2));
  console.log('Metadata saved successfully');
  
} catch (error) {
  console.error('Error:', error.message);
}
