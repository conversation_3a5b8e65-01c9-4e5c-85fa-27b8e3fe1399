const fs = require('fs');

console.log('=== VALIDATING ENCODING FIX RESULTS ===\n');

function validateJSONParsing(filename) {
  console.log(`\n=== VALIDATING ${filename.toUpperCase()} ===`);
  
  try {
    // Read and parse JSON
    const content = fs.readFileSync(filename, 'utf8');
    const jsonData = JSON.parse(content);
    
    console.log(`✅ JSON parsing successful`);
    console.log(`  File size: ${content.length} characters`);
    console.log(`  Root keys: ${Object.keys(jsonData).length}`);
    
    // Validate structure
    const dependencias = Object.keys(jsonData);
    let totalRecords = 0;
    let contactFieldsFound = 0;
    let spanishCharactersFound = [];
    
    dependencias.forEach(depCode => {
      const dep = jsonData[depCode];
      
      // Check dependencia level
      if (dep.correo) contactFieldsFound++;
      if (dep.direccion) contactFieldsFound++;
      if (dep.extension) contactFieldsFound++;
      if (dep.responsable) contactFieldsFound++;
      
      // Check for Spanish characters in dependencia
      [dep.correo, dep.direccion, dep.responsable].forEach(field => {
        if (field && /[áéíóúñüÁÉÍÓÚÑÜ]/.test(field)) {
          spanishCharactersFound.push({
            level: 'dependencia',
            code: depCode,
            field: field.substring(0, 50) + '...'
          });
        }
      });
      
      // Check subdependencias
      Object.keys(dep).forEach(subCode => {
        if (subCode !== 'nombre' && subCode !== 'correo' && subCode !== 'direccion' && 
            subCode !== 'extension' && subCode !== 'responsable') {
          const sub = dep[subCode];
          
          if (sub.correo) contactFieldsFound++;
          if (sub.direccion) contactFieldsFound++;
          if (sub.extension) contactFieldsFound++;
          if (sub.responsable) contactFieldsFound++;
          
          // Check for Spanish characters in subdependencia
          [sub.correo, sub.direccion, sub.responsable].forEach(field => {
            if (field && /[áéíóúñüÁÉÍÓÚÑÜ]/.test(field)) {
              spanishCharactersFound.push({
                level: 'subdependencia',
                code: `${depCode}-${subCode}`,
                field: field.substring(0, 50) + '...'
              });
            }
          });
          
          // Count records
          if (filename.includes('tramites')) {
            totalRecords += sub.tramites ? sub.tramites.length : 0;
          } else {
            totalRecords += sub.OPA ? sub.OPA.length : 0;
          }
        }
      });
    });
    
    console.log(`  Total organizational units: ${dependencias.length}`);
    console.log(`  Total records: ${totalRecords}`);
    console.log(`  Contact fields found: ${contactFieldsFound}`);
    console.log(`  Spanish characters found: ${spanishCharactersFound.length}`);
    
    // Show examples of properly encoded Spanish characters
    if (spanishCharactersFound.length > 0) {
      console.log(`\n  Examples of properly encoded Spanish text:`);
      spanishCharactersFound.slice(0, 5).forEach((example, index) => {
        console.log(`    ${index + 1}. ${example.level} ${example.code}: ${example.field}`);
      });
    }
    
    // Check for any remaining corruption
    const corruptionCheck = content.match(/�/g);
    if (corruptionCheck) {
      console.log(`❌ Warning: ${corruptionCheck.length} � characters still found`);
    } else {
      console.log(`✅ No corruption characters (�) found`);
    }
    
    return {
      filename: filename,
      valid: true,
      fileSize: content.length,
      totalRecords: totalRecords,
      contactFields: contactFieldsFound,
      spanishCharacters: spanishCharactersFound.length,
      remainingCorruption: corruptionCheck ? corruptionCheck.length : 0
    };
    
  } catch (error) {
    console.log(`❌ Validation failed: ${error.message}`);
    return {
      filename: filename,
      valid: false,
      error: error.message
    };
  }
}

function testSpecificSpanishCharacters(filename) {
  console.log(`\n=== TESTING SPECIFIC SPANISH CHARACTERS IN ${filename.toUpperCase()} ===`);
  
  const content = fs.readFileSync(filename, 'utf8');
  
  // Test specific patterns that were corrupted
  const testPatterns = [
    { pattern: /Número/g, description: 'Número (street numbers)' },
    { pattern: /MARÍA/g, description: 'MARÍA (name)' },
    { pattern: /CASTAÑEDA/g, description: 'CASTAÑEDA (surname)' },
    { pattern: /SÁNCHEZ/g, description: 'SÁNCHEZ (surname)' },
    { pattern: /MUÑOZ/g, description: 'MUÑOZ (surname)' },
    { pattern: /RAMÍREZ/g, description: 'RAMÍREZ (surname)' },
    { pattern: /MILLÁN/g, description: 'MILLÁN (surname)' },
    { pattern: /JOSÉ/g, description: 'JOSÉ (name)' },
    { pattern: /GÓMEZ/g, description: 'GÓMEZ (surname)' },
    { pattern: /RODRÍGUEZ/g, description: 'RODRÍGUEZ (surname)' },
    { pattern: /CHACÓN/g, description: 'CHACÓN (surname)' },
    { pattern: /ZUÑIGA/g, description: 'ZUÑIGA (surname)' }
  ];
  
  const results = [];
  testPatterns.forEach(({ pattern, description }) => {
    const matches = content.match(pattern);
    if (matches) {
      console.log(`✅ ${description}: ${matches.length} occurrences found`);
      results.push({ pattern: description, count: matches.length });
    }
  });
  
  return results;
}

try {
  const files = ['tramites_chia_optimo.json', 'OPA-chia-optimo.json'];
  const validationResults = [];
  
  files.forEach(filename => {
    const result = validateJSONParsing(filename);
    validationResults.push(result);
    
    if (result.valid) {
      const spanishTests = testSpecificSpanishCharacters(filename);
      result.spanishTests = spanishTests;
    }
  });
  
  console.log('\n=== OVERALL VALIDATION SUMMARY ===');
  const allValid = validationResults.every(r => r.valid);
  const totalRecords = validationResults.reduce((sum, r) => sum + (r.totalRecords || 0), 0);
  const totalContactFields = validationResults.reduce((sum, r) => sum + (r.contactFields || 0), 0);
  const totalSpanishChars = validationResults.reduce((sum, r) => sum + (r.spanishCharacters || 0), 0);
  const totalCorruption = validationResults.reduce((sum, r) => sum + (r.remainingCorruption || 0), 0);
  
  console.log(`All files valid JSON: ${allValid ? '✅' : '❌'}`);
  console.log(`Total records across files: ${totalRecords}`);
  console.log(`Total contact fields: ${totalContactFields}`);
  console.log(`Spanish characters found: ${totalSpanishChars}`);
  console.log(`Remaining corruption: ${totalCorruption}`);
  
  // Save validation report
  const validationReport = {
    timestamp: new Date().toISOString(),
    validation_type: 'post_encoding_fix',
    files_validated: validationResults,
    summary: {
      all_files_valid: allValid,
      total_records: totalRecords,
      total_contact_fields: totalContactFields,
      spanish_characters_found: totalSpanishChars,
      remaining_corruption: totalCorruption
    }
  };
  
  fs.writeFileSync('encoding_validation_report.json', JSON.stringify(validationReport, null, 2));
  console.log('\n✅ Validation report saved to encoding_validation_report.json');
  
  if (allValid && totalCorruption === 0) {
    console.log('\n🎉 ENCODING VALIDATION SUCCESSFUL!');
    console.log('All JSON files are valid and Spanish characters display correctly.');
  } else {
    console.log('\n⚠️  ENCODING VALIDATION ISSUES DETECTED');
    console.log('Manual review may be required.');
  }
  
} catch (error) {
  console.error('❌ Error during validation:', error.message);
  process.exit(1);
}
