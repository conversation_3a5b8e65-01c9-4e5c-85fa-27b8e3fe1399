const fs = require('fs');

console.log('=== ANALYZING OPA FILE STRUCTURE FOR STANDARDIZATION ===\n');
console.log('Reading OPA file...');

// Read the OPA file with error handling
let opaData;
try {
  const fileContent = fs.readFileSync('OPA-chia-optimo.json', 'utf8');
  console.log(`File size: ${Math.round(fileContent.length / 1024)} KB`);

  console.log('Parsing JSON...');
  opaData = JSON.parse(fileContent);
  console.log('JSON parsed successfully\n');
} catch (error) {
  console.error('Error reading/parsing file:', error.message);
  process.exit(1);
}

let totalOPAs = 0;
let totalDependencias = 0;
let totalSubdependencias = 0;
const opaDistribution = {};

console.log('CURRENT OPA STRUCTURE ANALYSIS:\n');

Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
  totalDependencias++;
  console.log(`DEPENDENCIA ${depCode}: ${depData.nombre} (${depData.sigla})`);

  opaDistribution[depCode] = {
    nombre: depData.nombre,
    sigla: depData.sigla,
    subdependencias: {}
  };

  Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
    totalSubdependencias++;
    const opaCount = subData.OPA ? subData.OPA.length : 0;
    totalOPAs += opaCount;

    console.log(`  └─ SUBDEPENDENCIA ${subCode}: ${subData.nombre} (${subData.sigla}) - ${opaCount} OPAs`);

    if (opaCount > 0) {
      opaDistribution[depCode].subdependencias[subCode] = {
        nombre: subData.nombre,
        sigla: subData.sigla,
        opaCount: opaCount,
        currentCodes: subData.OPA.slice(0, 5).map(opa => opa.codigo_OPA), // Only store first 5 for memory efficiency
        expectedCodeRange: {
          from: `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-001`,
          to: `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${opaCount.toString().padStart(3, '0')}`
        }
      };

      // Show first few current codes as examples
      const sampleCodes = subData.OPA.slice(0, 3).map(opa => opa.codigo_OPA);
      console.log(`      Current codes: [${sampleCodes.join(', ')}${opaCount > 3 ? ', ...' : ''}]`);
      console.log(`      Expected range: ${opaDistribution[depCode].subdependencias[subCode].expectedCodeRange.from} to ${opaDistribution[depCode].subdependencias[subCode].expectedCodeRange.to}`);
    }
  });
  console.log('');
});

console.log(`=== SUMMARY ===`);
console.log(`Total Dependencias: ${totalDependencias}`);
console.log(`Total Subdependencias: ${totalSubdependencias}`);
console.log(`Total OPAs: ${totalOPAs}`);

// Analyze current codigo_OPA patterns (optimized for large files)
console.log(`\n=== CURRENT CODIGO_OPA ANALYSIS ===`);
let numericCodes = 0;
let nonNumericCodes = 0;
let maxCodeValue = 0;
const codePatterns = new Set();
const nonNumericExamples = [];

Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
  Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
    if (subData.OPA && subData.OPA.length > 0) {
      subData.OPA.forEach(opa => {
        const code = opa.codigo_OPA;
        codePatterns.add(code);

        if (/^\d+$/.test(code)) {
          numericCodes++;
          const numValue = parseInt(code);
          if (numValue > maxCodeValue) {
            maxCodeValue = numValue;
          }
        } else {
          nonNumericCodes++;
          if (nonNumericExamples.length < 10) { // Limit examples for performance
            nonNumericExamples.push(`"${code}" in ${depCode}-${subCode}`);
          }
        }
      });
    }
  });
});

console.log(`Numeric codes: ${numericCodes}`);
console.log(`Non-numeric codes: ${nonNumericCodes}`);
console.log(`Highest numeric code: ${maxCodeValue}`);
console.log(`Unique code patterns: ${codePatterns.size}`);

if (nonNumericExamples.length > 0) {
  console.log(`Non-numeric examples: ${nonNumericExamples.join(', ')}`);
}

// Check for potential conflicts with new format (optimized)
console.log(`\n=== STANDARDIZATION READINESS CHECK ===`);
const newFormatCodes = new Set();
let potentialConflicts = 0;

Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
  Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
    if (subData.OPA && subData.OPA.length > 0) {
      subData.OPA.forEach((opa, index) => {
        const newCode = `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${(index + 1).toString().padStart(3, '0')}`;

        if (newFormatCodes.has(newCode)) {
          console.log(`❌ Potential conflict: ${newCode} would be duplicated`);
          potentialConflicts++;
        } else {
          newFormatCodes.add(newCode);
        }
      });
    }
  });
});

console.log(`Expected unique codes after standardization: ${newFormatCodes.size}`);
console.log(`Potential conflicts: ${potentialConflicts}`);

if (potentialConflicts === 0 && newFormatCodes.size === totalOPAs) {
  console.log('✅ Ready for standardization - no conflicts expected');
} else {
  console.log('❌ Issues detected - review before proceeding');
}

// Save analysis results
const analysisReport = {
  timestamp: new Date().toISOString(),
  summary: {
    totalDependencias: totalDependencias,
    totalSubdependencias: totalSubdependencias,
    totalOPAs: totalOPAs,
    numericCodes: numericCodes,
    nonNumericCodes: nonNumericCodes,
    maxCodeValue: maxCodeValue,
    uniquePatterns: codePatterns.size,
    expectedUniqueCodesAfterStandardization: newFormatCodes.size,
    potentialConflicts: potentialConflicts
  },
  distribution: opaDistribution,
  readyForStandardization: potentialConflicts === 0 && newFormatCodes.size === totalOPAs
};

fs.writeFileSync('opa_structure_analysis.json', JSON.stringify(analysisReport, null, 2));
console.log('\n✅ Analysis saved to opa_structure_analysis.json');

console.log('\n=== ANALYSIS COMPLETE ===');
