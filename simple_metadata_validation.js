const fs = require('fs');

console.log('=== SIMPLE METADATA ENHANCEMENT VALIDATION ===\n');

try {
  // Load files
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  const enhancementReport = JSON.parse(fs.readFileSync('metadata_enhancement_report.json', 'utf8'));
  
  console.log('✅ All files loaded successfully');
  
  let tramitesContactFields = 0;
  let opaContactFields = 0;
  let tramitesDepsWithContact = 0;
  let opaDepsWithContact = 0;
  
  console.log('\n=== COUNTING ENHANCED CONTACT FIELDS ===');
  
  // Count tramites contact fields
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    const contactFields = ['correo', 'direccion', 'extension', 'responsable'];
    const hasContact = contactFields.some(field => depData[field]);
    if (hasContact) {
      tramitesDepsWithContact++;
      contactFields.forEach(field => {
        if (depData[field]) tramitesContactFields++;
      });
      console.log(`Tramites ${depCode}: ${contactFields.filter(f => depData[f]).join(', ')}`);
    }
    
    // Count subdependencias
    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        const hasSubContact = contactFields.some(field => subData[field]);
        if (hasSubContact) {
          contactFields.forEach(field => {
            if (subData[field]) tramitesContactFields++;
          });
          console.log(`  Sub ${subCode}: ${contactFields.filter(f => subData[f]).join(', ')}`);
        }
      });
    }
  });
  
  // Count OPA contact fields
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    const contactFields = ['correo', 'direccion', 'extension', 'responsable'];
    const hasContact = contactFields.some(field => depData[field]);
    if (hasContact) {
      opaDepsWithContact++;
      contactFields.forEach(field => {
        if (depData[field]) opaContactFields++;
      });
      console.log(`OPA ${depCode}: ${contactFields.filter(f => depData[f]).join(', ')}`);
    }
    
    // Count subdependencias
    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        const contactFields = ['correo', 'direccion', 'extension', 'responsable'];
        const hasSubContact = contactFields.some(field => subData[field]);
        if (hasSubContact) {
          contactFields.forEach(field => {
            if (subData[field]) opaContactFields++;
          });
        }
      });
    }
  });
  
  console.log('\n=== VALIDATION SUMMARY ===');
  console.log(`Tramites contact fields found: ${tramitesContactFields}`);
  console.log(`Expected tramites contact fields: ${enhancementReport.enhancement_log.tramites.contact_fields_added}`);
  console.log(`OPA contact fields found: ${opaContactFields}`);
  console.log(`Expected OPA contact fields: ${enhancementReport.enhancement_log.opa.contact_fields_added}`);
  
  const tramitesMatch = tramitesContactFields === enhancementReport.enhancement_log.tramites.contact_fields_added;
  const opaMatch = opaContactFields === enhancementReport.enhancement_log.opa.contact_fields_added;
  
  console.log(`Tramites enhancement: ${tramitesMatch ? '✅' : '❌'}`);
  console.log(`OPA enhancement: ${opaMatch ? '✅' : '❌'}`);
  
  if (tramitesMatch && opaMatch) {
    console.log('\n🎉 METADATA ENHANCEMENT VALIDATION PASSED!');
    console.log(`Total contact fields added: ${tramitesContactFields + opaContactFields}`);
    console.log(`Total enhancements applied: ${enhancementReport.total_enhancements}`);
  } else {
    console.log('\n❌ Metadata enhancement validation failed');
  }
  
} catch (error) {
  console.error('❌ Error during validation:', error.message);
}
