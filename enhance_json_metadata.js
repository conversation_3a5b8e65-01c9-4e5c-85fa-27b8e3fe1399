const fs = require('fs');

console.log('=== ENHANCING JSON METADATA WITH CSV DATA ===\n');

try {
  // Load files
  console.log('Loading files...');
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  const csvMapping = JSON.parse(fs.readFileSync('csv_enhancement_mapping.json', 'utf8'));
  const crossRefReport = JSON.parse(fs.readFileSync('metadata_cross_reference_report.json', 'utf8'));
  
  console.log('✅ All files loaded successfully');
  
  const enhancementLog = {
    tramites: {
      dependencias_enhanced: 0,
      subdependencias_enhanced: 0,
      contact_fields_added: 0
    },
    opa: {
      dependencias_enhanced: 0,
      subdependencias_enhanced: 0,
      contact_fields_added: 0
    },
    enhancements_applied: []
  };
  
  console.log('\n=== ENHANCING TRAMITES METADATA ===');
  
  // Enhance tramites data
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    const csvDepData = csvMapping.codigo_to_data[depCode];
    if (csvDepData) {
      console.log(`Enhancing dependencia ${depCode}: ${depData.nombre}`);
      
      // Add contact information
      const contactFields = {};
      if (csvDepData.correo) contactFields.correo = csvDepData.correo;
      if (csvDepData.direccion) contactFields.direccion = csvDepData.direccion;
      if (csvDepData.extension) contactFields.extension = csvDepData.extension;
      if (csvDepData.responsable) contactFields.responsable = csvDepData.responsable;
      
      if (Object.keys(contactFields).length > 0) {
        Object.assign(depData, contactFields);
        enhancementLog.tramites.dependencias_enhanced++;
        enhancementLog.tramites.contact_fields_added += Object.keys(contactFields).length;
        enhancementLog.enhancements_applied.push({
          type: 'dependencia',
          source: 'tramites',
          codigo: depCode,
          fields_added: Object.keys(contactFields)
        });
        console.log(`  ✅ Added: ${Object.keys(contactFields).join(', ')}`);
      }
    }
    
    // Enhance subdependencias
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      const csvSubData = csvMapping.codigo_to_data[subCode];
      if (csvSubData) {
        console.log(`  Enhancing subdependencia ${subCode}: ${subData.nombre}`);
        
        const contactFields = {};
        if (csvSubData.correo) contactFields.correo = csvSubData.correo;
        if (csvSubData.direccion) contactFields.direccion = csvSubData.direccion;
        if (csvSubData.extension) contactFields.extension = csvSubData.extension;
        if (csvSubData.responsable) contactFields.responsable = csvSubData.responsable;
        
        if (Object.keys(contactFields).length > 0) {
          Object.assign(subData, contactFields);
          enhancementLog.tramites.subdependencias_enhanced++;
          enhancementLog.tramites.contact_fields_added += Object.keys(contactFields).length;
          enhancementLog.enhancements_applied.push({
            type: 'subdependencia',
            source: 'tramites',
            codigo: subCode,
            parent_codigo: depCode,
            fields_added: Object.keys(contactFields)
          });
          console.log(`    ✅ Added: ${Object.keys(contactFields).join(', ')}`);
        }
      }
    });
  });
  
  console.log('\n=== ENHANCING OPA METADATA ===');
  
  // Enhance OPA data
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    const csvDepData = csvMapping.codigo_to_data[depCode];
    if (csvDepData) {
      console.log(`Enhancing OPA dependencia ${depCode}: ${depData.nombre}`);
      
      const contactFields = {};
      if (csvDepData.correo) contactFields.correo = csvDepData.correo;
      if (csvDepData.direccion) contactFields.direccion = csvDepData.direccion;
      if (csvDepData.extension) contactFields.extension = csvDepData.extension;
      if (csvDepData.responsable) contactFields.responsable = csvDepData.responsable;
      
      if (Object.keys(contactFields).length > 0) {
        Object.assign(depData, contactFields);
        enhancementLog.opa.dependencias_enhanced++;
        enhancementLog.opa.contact_fields_added += Object.keys(contactFields).length;
        enhancementLog.enhancements_applied.push({
          type: 'dependencia',
          source: 'opa',
          codigo: depCode,
          fields_added: Object.keys(contactFields)
        });
        console.log(`  ✅ Added: ${Object.keys(contactFields).join(', ')}`);
      }
    }
    
    // Enhance OPA subdependencias
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      const csvSubData = csvMapping.codigo_to_data[subCode];
      if (csvSubData) {
        const contactFields = {};
        if (csvSubData.correo) contactFields.correo = csvSubData.correo;
        if (csvSubData.direccion) contactFields.direccion = csvSubData.direccion;
        if (csvSubData.extension) contactFields.extension = csvSubData.extension;
        if (csvSubData.responsable) contactFields.responsable = csvSubData.responsable;
        
        if (Object.keys(contactFields).length > 0) {
          Object.assign(subData, contactFields);
          enhancementLog.opa.subdependencias_enhanced++;
          enhancementLog.opa.contact_fields_added += Object.keys(contactFields).length;
          enhancementLog.enhancements_applied.push({
            type: 'subdependencia',
            source: 'opa',
            codigo: subCode,
            parent_codigo: depCode,
            fields_added: Object.keys(contactFields)
          });
        }
      }
    });
  });
  
  console.log('\n=== ENHANCEMENT SUMMARY ===');
  console.log('TRAMITES:');
  console.log(`  Dependencias enhanced: ${enhancementLog.tramites.dependencias_enhanced}`);
  console.log(`  Subdependencias enhanced: ${enhancementLog.tramites.subdependencias_enhanced}`);
  console.log(`  Contact fields added: ${enhancementLog.tramites.contact_fields_added}`);
  
  console.log('OPA:');
  console.log(`  Dependencias enhanced: ${enhancementLog.opa.dependencias_enhanced}`);
  console.log(`  Subdependencias enhanced: ${enhancementLog.opa.subdependencias_enhanced}`);
  console.log(`  Contact fields added: ${enhancementLog.opa.contact_fields_added}`);
  
  console.log(`Total enhancements applied: ${enhancementLog.enhancements_applied.length}`);
  
  // Save enhanced files
  fs.writeFileSync('tramites_chia_optimo.json', JSON.stringify(tramitesData, null, 2));
  console.log('\n✅ Enhanced tramites_chia_optimo.json saved');
  
  fs.writeFileSync('OPA-chia-optimo.json', JSON.stringify(opaData, null, 2));
  console.log('✅ Enhanced OPA-chia-optimo.json saved');
  
  // Save enhancement report
  const enhancementReport = {
    timestamp: new Date().toISOString(),
    enhancement_type: 'csv_metadata_integration',
    enhancement_log: enhancementLog,
    total_enhancements: enhancementLog.enhancements_applied.length,
    total_contact_fields: enhancementLog.tramites.contact_fields_added + enhancementLog.opa.contact_fields_added
  };
  
  fs.writeFileSync('metadata_enhancement_report.json', JSON.stringify(enhancementReport, null, 2));
  console.log('✅ Enhancement report saved to metadata_enhancement_report.json');
  
  console.log('\n🎉 METADATA ENHANCEMENT COMPLETED SUCCESSFULLY!');
  
} catch (error) {
  console.error('❌ Error during enhancement:', error.message);
  process.exit(1);
}
