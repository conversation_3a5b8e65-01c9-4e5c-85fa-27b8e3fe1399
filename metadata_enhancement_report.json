{"timestamp": "2025-07-08T15:02:56.680Z", "enhancement_type": "csv_metadata_integration", "enhancement_log": {"tramites": {"dependencias_enhanced": 9, "subdependencias_enhanced": 12, "contact_fields_added": 84}, "opa": {"dependencias_enhanced": 13, "subdependencias_enhanced": 54, "contact_fields_added": 267}, "enhancements_applied": [{"type": "dependencia", "source": "tramites", "codigo": "100", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "113", "parent_codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "041", "parent_codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "091", "parent_codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "001", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "073", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "071", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "030", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "032", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "013", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "014", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "tramites", "codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "081", "parent_codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "tramites", "codigo": "082", "parent_codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "100", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "110", "parent_codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "111", "parent_codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "113", "parent_codigo": "110", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "120", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "120", "parent_codigo": "120", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "000", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "001", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "002", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "003", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "004", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "005", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "006", "parent_codigo": "000", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "010", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "012", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "013", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "014", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "015", "parent_codigo": "010", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "020", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "020", "parent_codigo": "020", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "021", "parent_codigo": "020", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "022", "parent_codigo": "020", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "330", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "331", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "332", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "333", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "334", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "335", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension"]}, {"type": "subdependencia", "source": "opa", "codigo": "336", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "337", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "338", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "339", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "030", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "031", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "032", "parent_codigo": "030", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "040", "parent_codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "041", "parent_codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "042", "parent_codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "043", "parent_codigo": "040", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "050", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "050", "parent_codigo": "050", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "051", "parent_codigo": "050", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "052", "parent_codigo": "050", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "053", "parent_codigo": "050", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "060", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "060", "parent_codigo": "060", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "061", "parent_codigo": "060", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "062", "parent_codigo": "060", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "063", "parent_codigo": "060", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "070", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "071", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "072", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "073", "parent_codigo": "070", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "080", "parent_codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "081", "parent_codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "082", "parent_codigo": "080", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "dependencia", "source": "opa", "codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "090", "parent_codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "091", "parent_codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}, {"type": "subdependencia", "source": "opa", "codigo": "092", "parent_codigo": "090", "fields_added": ["correo", "direccion", "extension", "responsable"]}]}, "total_enhancements": 88, "total_contact_fields": 351}