{"dependencias": {"100": {"nombre": "Secretaria de Medio Ambiente", "sigla": "SMA", "subdependencias": {"101": {"nombre": "Forestal", "sigla": "SMA"}, "102": {"nombre": "Control y conservación ambiental", "sigla": "SMA"}, "103": {"nombre": "Sistema <PERSON>", "sigla": "SMA"}, "104": {"nombre": "Informes Técnicos", "sigla": "SMA"}, "105": {"nombre": "PGIRS", "sigla": "SMA"}, "106": {"nombre": "Biodiversidad", "sigla": "SMA"}, "107": {"nombre": "Educación ambiental", "sigla": "SMA"}, "108": {"nombre": "Visita Técnica vigilancia y control fuentes contaminantes", "sigla": "SMA"}, "109": {"nombre": "Publicidad", "sigla": "SMA"}}}, "110": {"nombre": "Secretaria de Movilidad", "sigla": "SM", "subdependencias": {"110": {"nombre": "Directo", "sigla": "SM"}, "111": {"nombre": "Dirección de Servicios de Movilidad y Gestión del Transporte", "sigla": "DSMGT"}, "112": {"nombre": "Dirección de Educación, Seguridad\nVial y Control de Transito", "sigla": "DESVCT"}, "113": {"nombre": "Unión Temporal Circulemos Chía", "sigla": "UTCCH"}}}, "120": {"nombre": "Secretaria de Participacion Ciudadana y Accion Comunitaria", "sigla": "SPCAC", "subdependencias": {"120": {"nombre": "Directo", "sigla": "SPCAC"}}}, "200": {"nombre": "Descentralizados", "sigla": "SPCAC", "subdependencias": {"201": {"nombre": "IDUVI", "sigla": "SPCAC"}, "202": {"nombre": "IMRD", "sigla": "SPCAC"}, "203": {"nombre": "EMSERCHIA", "sigla": "SPCAC"}, "204": {"nombre": "PERSONERIA", "sigla": "SPCAC"}, "212": {"nombre": "AGUSTIN CODAZZI", "sigla": "SPCAC"}, "214": {"nombre": "CUERPO DE BOMBEROS  CHIA", "sigla": "SPCAC"}, "215": {"nombre": "DEFENSA CIVIL COLOMBIANA", "sigla": "SPCAC"}}}, "000": {"nombre": "<PERSON><PERSON><PERSON>", "sigla": "DA", "subdependencias": {"000": {"nombre": "Directo", "sigla": "DA"}, "001": {"nombre": "Oficina Asesora <PERSON>í<PERSON>", "sigla": "OAJ"}, "002": {"nombre": "Oficina de Contratación", "sigla": "OC"}, "003": {"nombre": "Oficina <PERSON>", "sigla": "ODJ"}, "004": {"nombre": "Oficina de Control Interno", "sigla": "OCI"}, "005": {"nombre": "Oficina de tecnologías de la información y las comunicaciones TICS", "sigla": "OTIC"}, "006": {"nombre": "Oficina Asesora de comunicación Prensa y Protocolo", "sigla": "OACPP"}}}, "010": {"nombre": "Secretaria de Planeacion", "sigla": "SP", "subdependencias": {"010": {"nombre": "Directo", "sigla": "SP"}, "011": {"nombre": "Dirección Sistemas de la Información\ny Estadísticas", "sigla": "OSIE"}, "012": {"nombre": "Dirección de Planificación del Desarrollo", "sigla": "DPD"}, "013": {"nombre": "Dirección de Ordenamiento Territorial y Plusvalía", "sigla": "DOTP"}, "014": {"nombre": "Dirección de Urbanismo", "sigla": "DU"}, "015": {"nombre": "Dirección de Servicios <PERSON>", "sigla": "DSP"}}}, "020": {"nombre": "Secretaria General", "sigla": "SG", "subdependencias": {"020": {"nombre": "Directo", "sigla": "SG"}, "021": {"nombre": "Dirección de Función Publica", "sigla": "DFP"}, "022": {"nombre": "Dirección de Servicios Administrativos", "sigla": "DSA"}, "023": {"nombre": "Dirección Centro de Atención al Ciudadano", "sigla": "DCAC"}, "024": {"nombre": "Dirección de Control Interno Disciplinario", "sigla": "DCID"}}}, "030": {"nombre": "Secretaria de Gobierno", "sigla": "SG", "subdependencias": {"330": {"nombre": "Comisaria Primera de Familia", "sigla": "CPF"}, "331": {"nombre": "Comisaria Segunda de Familia", "sigla": "CSF"}, "332": {"nombre": "Comisaria Tercera de Familia", "sigla": "CTF"}, "333": {"nombre": "Comisaria Cuarta de Familia", "sigla": "CCF"}, "334": {"nombre": "Inspección Primera de Policía", "sigla": "IPP"}, "335": {"nombre": "Inspección Segunda de Policía", "sigla": "PSP"}, "336": {"nombre": "Inspección Tercera de Policía", "sigla": "ITP"}, "337": {"nombre": "Inspección Cuarta de Policía", "sigla": "ICP"}, "338": {"nombre": "Inspección Quinta de Policía", "sigla": "IQP"}, "339": {"nombre": "Inspección Sexta de Policía", "sigla": "ISP"}, "030": {"nombre": "Directo", "sigla": "SG"}, "031": {"nombre": "Dirección de seguridad y Convivencia Ciudadana", "sigla": "DSCC"}, "032": {"nombre": "Dirección de Asuntos Étnicos Raciales Religiosos y Posconflicto", "sigla": "DAERRP"}, "033": {"nombre": "Dirección de Derechos y Resolución de Conflictos", "sigla": "DDRC"}}}, "040": {"nombre": "Secretaria de Hacienda", "sigla": "SH", "subdependencias": {"040": {"nombre": "Directo", "sigla": "SH"}, "041": {"nombre": "Dirección de Rentas", "sigla": "DR"}, "042": {"nombre": "Dirección Financiera", "sigla": "DF"}, "043": {"nombre": "Dirección de Tesorería", "sigla": "DT"}}}, "050": {"nombre": "Secretaria de Obras Publicas", "sigla": "SOP", "subdependencias": {"050": {"nombre": "Directo", "sigla": "SOP"}, "051": {"nombre": "Dirección de Infraestructura", "sigla": "DI"}, "052": {"nombre": "Dirección de Programación, Estudios y Diseños", "sigla": "DPED"}, "053": {"nombre": "Dirección de Valorización", "sigla": "DV"}}}, "060": {"nombre": "Secretaria de Desarrollo Social", "sigla": "SDS", "subdependencias": {"060": {"nombre": "Directo", "sigla": "SDS"}, "061": {"nombre": "Dirección de Ciudadanía Juvenil", "sigla": "DCJ"}, "062": {"nombre": "Dirección de Acción Social", "sigla": "DAS"}, "063": {"nombre": "Dirección de Cultura", "sigla": "DC"}}}, "070": {"nombre": "Secretaria de Educacion", "sigla": "SE", "subdependencias": {"070": {"nombre": "Directo", "sigla": "SE"}, "071": {"nombre": "Dirección de Inspección y Vigilancia", "sigla": "DIV"}, "072": {"nombre": "Dirección de Gestión y Fomento a la Educación", "sigla": "DGFE"}, "073": {"nombre": "Dirección Administrativa y Financiera", "sigla": "DAF"}}}, "080": {"nombre": "Secretaria de Salud", "sigla": "SS", "subdependencias": {"080": {"nombre": "Directo", "sigla": "SS"}, "081": {"nombre": "Dirección de Salud Publica", "sigla": "DSP"}, "082": {"nombre": "Dirección de Vigilancia y Control", "sigla": "DVC"}}}, "090": {"nombre": "Secretaria para el Desarrollo Economico", "sigla": "SDE", "subdependencias": {"090": {"nombre": "Directo", "sigla": "SDE"}, "091": {"nombre": "Dirección de Desarrollo Agropecuario y Empresarial", "sigla": "DDAE"}, "092": {"nombre": "Dirección de Turismo", "sigla": "DT"}}}}}