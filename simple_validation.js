const fs = require('fs');

console.log('Validating nested tramites structure...');

try {
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  console.log('Tramites file loaded successfully');
  
  let totalTramites = 0;
  const codes = new Set();
  
  console.log('\nStructure validation:');
  
  // Check root structure
  if (tramitesData.dependencias) {
    console.log('✅ Root "dependencias" field present');
  } else {
    console.log('❌ Missing root "dependencias" field');
    process.exit(1);
  }
  
  // Count and validate structure
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    console.log(`${depCode}: ${depData.nombre} (${depData.sigla})`);
    
    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        console.log(`  └─ ${subCode}: ${subData.nombre} - ${subData.tramites?.length || 0} tramites`);
        
        if (subData.tramites && Array.isArray(subData.tramites)) {
          subData.tramites.forEach(tramite => {
            totalTramites++;
            if (tramite.codigo_unico) {
              codes.add(tramite.codigo_unico);
            }
          });
        }
      });
    }
  });
  
  console.log(`\nValidation Summary:`);
  console.log(`Total tramites: ${totalTramites}`);
  console.log(`Unique codes: ${codes.size}`);
  console.log(`Dependencias: ${Object.keys(tramitesData.dependencias).length}`);
  
  if (totalTramites === 108 && codes.size === 108) {
    console.log('✅ Validation passed - all tramites preserved with unique codes');
  } else {
    console.log('❌ Validation failed - count mismatch');
  }
  
} catch (error) {
  console.error('Error:', error.message);
}
