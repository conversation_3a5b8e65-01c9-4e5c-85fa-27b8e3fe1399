const fs = require('fs');

console.log('=== IDENTIFYING CORRUPTED CHARACTERS IN DETAIL ===\n');

// Comprehensive mapping of corrupted characters to correct ones
const characterMappings = {
  // Common corruption patterns found
  'N�mero': 'Númer<PERSON>',
  'MAR�A': 'MARÍA',
  'CASTA�EDA': 'CASTAÑEDA',
  'S�NCHEZ': 'SÁNCHEZ',
  
  // Extension separators (� used instead of -)
  ' � ': ' - ',
  '�': '-',  // Generic replacement for dash/hyphen
  
  // Additional corrupted names found in analysis
  'MU�OZ': 'MUÑOZ',
  'RAM�REZ': 'RAMÍREZ',
  'MILL�N': 'MILLÁN',
  'JOS�': 'JOSÉ',
  'G�MEZ': 'GÓMEZ',
  'RODR�GUEZ': 'RODRÍGUEZ',
  'CHAC�N': 'CHACÓN',
  'ZU�IGA': 'ZUÑIGA',

  // Other potential Spanish character corruptions
  'Administraci�n': 'Administración',
  'Educaci�n': 'Educación',
  'Informaci�n': 'Información',
  'Participaci�n': 'Participación',
  'Atenci�n': 'Atención',
  'Prevenci�n': 'Prevención',
  'Protecci�n': 'Protección',
  'Inspecci�n': 'Inspección',
  'Vigilancia': 'Vigilancia',
  'Tr�nsito': 'Tránsito',
  'Tr�mite': 'Trámite',
  'Tr�mites': 'Trámites',
  'P�blica': 'Pública',
  'P�blico': 'Público',
  'Econ�mico': 'Económico',
  'Agr�cola': 'Agrícola',
  'Jur�dica': 'Jurídica',
  'T�cnico': 'Técnico',
  'M�dico': 'Médico',
  'Acad�mico': 'Académico',
  'Pol�tica': 'Política',
  'Hist�rico': 'Histórico',
  'Geogr�fico': 'Geográfico',
  'Estad�stica': 'Estadística',
  'Log�stica': 'Logística',
  'Energ�a': 'Energía',
  'Bater�a': 'Batería',
  'Categor�a': 'Categoría',
  'Secretar�a': 'Secretaría',
  'Tesorer�a': 'Tesorería',
  'Contralor�a': 'Contraloría',
  'Procuradur�a': 'Procuraduría',
  'Registradur�a': 'Registraduría'
};

function analyzeCorruptedCharacters(content, filename) {
  console.log(`\n=== ANALYZING ${filename.toUpperCase()} ===`);
  
  const findings = {
    filename: filename,
    total_corruptions: 0,
    corruption_details: [],
    affected_fields: {
      direccion: 0,
      responsable: 0,
      correo: 0,
      extension: 0,
      nombres: 0,
      other: 0
    },
    unique_corruptions: new Set()
  };
  
  // Check each mapping
  Object.entries(characterMappings).forEach(([corrupted, correct]) => {
    const regex = new RegExp(corrupted.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    const matches = content.match(regex);
    
    if (matches) {
      const count = matches.length;
      findings.total_corruptions += count;
      findings.unique_corruptions.add(corrupted);
      
      console.log(`❌ "${corrupted}" → "${correct}": ${count} occurrences`);
      
      findings.corruption_details.push({
        corrupted: corrupted,
        correct: correct,
        count: count
      });
      
      // Find context for each occurrence
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (line.includes(corrupted)) {
          console.log(`    Line ${index + 1}: ${line.trim().substring(0, 100)}...`);
          
          // Categorize by field type
          if (line.includes('"direccion"')) findings.affected_fields.direccion++;
          else if (line.includes('"responsable"')) findings.affected_fields.responsable++;
          else if (line.includes('"correo"')) findings.affected_fields.correo++;
          else if (line.includes('"extension"')) findings.affected_fields.extension++;
          else if (line.includes('"nombre"')) findings.affected_fields.nombres++;
          else findings.affected_fields.other++;
        }
      });
    }
  });
  
  // Look for any remaining � characters that might not be in our mapping
  const remainingCorruption = content.match(/�/g);
  if (remainingCorruption) {
    const unmappedCount = remainingCorruption.length;
    console.log(`⚠️  Additional unmapped � characters: ${unmappedCount}`);
    
    // Find contexts for unmapped characters
    const lines = content.split('\n');
    const unmappedContexts = [];
    lines.forEach((line, index) => {
      if (line.includes('�') && !Array.from(findings.unique_corruptions).some(corruption => line.includes(corruption))) {
        unmappedContexts.push(`Line ${index + 1}: ${line.trim()}`);
      }
    });
    
    if (unmappedContexts.length > 0) {
      console.log('    Unmapped contexts:');
      unmappedContexts.slice(0, 5).forEach(context => {
        console.log(`      ${context.substring(0, 120)}...`);
      });
    }
  }
  
  console.log(`\nSummary for ${filename}:`);
  console.log(`  Total corruptions: ${findings.total_corruptions}`);
  console.log(`  Unique corruption types: ${findings.unique_corruptions.size}`);
  console.log(`  Affected fields:`);
  Object.entries(findings.affected_fields).forEach(([field, count]) => {
    if (count > 0) console.log(`    ${field}: ${count}`);
  });
  
  return findings;
}

try {
  const files = ['tramites_chia_optimo.json', 'OPA-chia-optimo.json'];
  const allFindings = [];
  
  files.forEach(filename => {
    const content = fs.readFileSync(filename, 'utf8');
    const findings = analyzeCorruptedCharacters(content, filename);
    allFindings.push(findings);
  });
  
  console.log('\n=== OVERALL CORRUPTION ANALYSIS ===');
  const totalCorruptions = allFindings.reduce((sum, f) => sum + f.total_corruptions, 0);
  const totalUniqueTypes = new Set();
  allFindings.forEach(f => f.unique_corruptions.forEach(c => totalUniqueTypes.add(c)));
  
  console.log(`Total corruptions across all files: ${totalCorruptions}`);
  console.log(`Unique corruption types: ${totalUniqueTypes.size}`);
  
  // Save detailed findings
  const corruptionReport = {
    timestamp: new Date().toISOString(),
    analysis_type: 'character_corruption_identification',
    character_mappings: characterMappings,
    files_analyzed: allFindings,
    summary: {
      total_corruptions: totalCorruptions,
      unique_corruption_types: totalUniqueTypes.size,
      corruption_types: Array.from(totalUniqueTypes)
    }
  };
  
  fs.writeFileSync('character_corruption_analysis.json', JSON.stringify(corruptionReport, null, 2));
  console.log('\n✅ Detailed corruption analysis saved to character_corruption_analysis.json');
  
} catch (error) {
  console.error('❌ Error during character analysis:', error.message);
  process.exit(1);
}
