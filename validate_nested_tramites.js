const fs = require('fs');

console.log('=== VALIDATING NESTED TRAMITES STRUCTURE ===\n');

try {
  console.log('Loading files for validation...');
  
  // Load transformed tramites
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  console.log('✅ Nested tramites loaded');
  
  // Load OPA structure for comparison
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  console.log('✅ OPA structure loaded for comparison');
  
  console.log('\n=== STRUCTURE VALIDATION ===\n');
  
  const validation = {
    structureMatch: true,
    fieldNaming: true,
    codePreservation: true,
    hierarchyConsistency: true,
    issues: []
  };
  
  // 1. Validate root structure
  console.log('1. Validating root structure...');
  if (!tramitesData.dependencias) {
    validation.structureMatch = false;
    validation.issues.push('Missing root "dependencias" field');
  } else {
    console.log('✅ Root "dependencias" field present');
  }
  
  // 2. Validate dependencia structure
  console.log('\n2. Validating dependencia structure...');
  let totalTramites = 0;
  const uniqueCodes = new Set();
  const codeFormatRegex = /^\d{3}-\d{3}-\d{3}$/;
  
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Validating dependencia ${depCode}: ${depData.nombre}`);
    
    // Check required fields
    if (!depData.nombre || !depData.sigla || !depData.subdependencias) {
      validation.structureMatch = false;
      validation.issues.push(`Dependencia ${depCode} missing required fields`);
    }
    
    // Validate subdependencias
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      console.log(`  └─ Validating subdependencia ${subCode}: ${subData.nombre}`);
      
      // Check required fields
      if (!subData.nombre || !subData.sigla || !subData.tramites) {
        validation.structureMatch = false;
        validation.issues.push(`Subdependencia ${depCode}-${subCode} missing required fields`);
      }
      
      // Validate tramites array
      if (!Array.isArray(subData.tramites)) {
        validation.structureMatch = false;
        validation.issues.push(`Subdependencia ${depCode}-${subCode} tramites is not an array`);
      } else {
        subData.tramites.forEach((tramite, index) => {
          totalTramites++;
          
          // Validate tramite structure
          const requiredFields = ['codigo_unico', 'nombre', 'formulario', 'tiempo_respuesta', 'tiene_pago'];
          requiredFields.forEach(field => {
            if (tramite[field] === undefined) {
              validation.fieldNaming = false;
              validation.issues.push(`Tramite ${tramite.codigo_unico || 'unknown'} missing field: ${field}`);
            }
          });
          
          // Validate code format
          if (tramite.codigo_unico) {
            if (!codeFormatRegex.test(tramite.codigo_unico)) {
              validation.codePreservation = false;
              validation.issues.push(`Invalid code format: ${tramite.codigo_unico}`);
            }
            
            // Check for duplicates
            if (uniqueCodes.has(tramite.codigo_unico)) {
              validation.codePreservation = false;
              validation.issues.push(`Duplicate code: ${tramite.codigo_unico}`);
            } else {
              uniqueCodes.add(tramite.codigo_unico);
            }
            
            // Validate code matches hierarchy
            const expectedPrefix = `${depCode}-${subCode}`;
            if (!tramite.codigo_unico.startsWith(expectedPrefix)) {
              validation.hierarchyConsistency = false;
              validation.issues.push(`Code ${tramite.codigo_unico} doesn't match hierarchy ${expectedPrefix}`);
            }
          }
        });
      }
    });
  });
  
  // 3. Compare with OPA structure format
  console.log('\n3. Comparing with OPA structure format...');
  
  // Check if structure mirrors OPA format
  const opaDepCount = Object.keys(opaData.dependencias).length;
  const tramitesDepCount = Object.keys(tramitesData.dependencias).length;
  
  console.log(`OPA dependencias: ${opaDepCount}`);
  console.log(`Tramites dependencias: ${tramitesDepCount}`);
  
  // The tramites might not have all dependencias (only those with tramites)
  if (tramitesDepCount > opaDepCount) {
    validation.structureMatch = false;
    validation.issues.push('Tramites has more dependencias than OPA structure');
  }
  
  // 4. Final validation summary
  console.log('\n=== VALIDATION RESULTS ===');
  console.log(`Total tramites processed: ${totalTramites}`);
  console.log(`Unique codes found: ${uniqueCodes.size}`);
  console.log(`Structure match: ${validation.structureMatch ? '✅' : '❌'}`);
  console.log(`Field naming: ${validation.fieldNaming ? '✅' : '❌'}`);
  console.log(`Code preservation: ${validation.codePreservation ? '✅' : '❌'}`);
  console.log(`Hierarchy consistency: ${validation.hierarchyConsistency ? '✅' : '❌'}`);
  
  if (validation.issues.length > 0) {
    console.log('\n❌ ISSUES FOUND:');
    validation.issues.forEach(issue => console.log(`  - ${issue}`));
  } else {
    console.log('\n✅ NO ISSUES FOUND');
  }
  
  // Save validation report
  const validationReport = {
    timestamp: new Date().toISOString(),
    validation_type: 'nested_structure_validation',
    total_tramites: totalTramites,
    unique_codes: uniqueCodes.size,
    dependencias_count: Object.keys(tramitesData.dependencias).length,
    subdependencias_count: Object.values(tramitesData.dependencias)
      .reduce((sum, dep) => sum + Object.keys(dep.subdependencias).length, 0),
    validation_results: validation,
    overall_success: validation.structureMatch && validation.fieldNaming && 
                    validation.codePreservation && validation.hierarchyConsistency
  };
  
  fs.writeFileSync('nested_tramites_validation.json', JSON.stringify(validationReport, null, 2));
  console.log('\n✅ Validation report saved to nested_tramites_validation.json');
  
  if (validationReport.overall_success) {
    console.log('\n🎉 VALIDATION PASSED - Structure transformation successful!');
  } else {
    console.log('\n❌ VALIDATION FAILED - Review issues before proceeding');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Error during validation:', error.message);
  process.exit(1);
}
