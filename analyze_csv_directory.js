const fs = require('fs');

console.log('=== ANALYZING CSV DIRECTORY STRUCTURE ===\n');

try {
  // Read CSV file
  const csvContent = fs.readFileSync('directorios-funcionarios-dependencias.csv', 'utf8');
  const lines = csvContent.split('\n').filter(line => line.trim());
  
  console.log(`Total lines in CSV: ${lines.length}`);
  
  // Parse header
  const header = lines[0].split(',');
  console.log('CSV Headers:', header);
  
  const organizationalData = {
    dependencias: {},
    subdependencias: {},
    contactInfo: {},
    siglaMapping: {},
    codigoMapping: {}
  };
  
  let totalRecords = 0;
  let recordsWithContact = 0;
  
  // Process each line (skip header)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line.trim()) continue;
    
    // Split CSV line (handling commas within quotes)
    const fields = [];
    let current = '';
    let inQuotes = false;
    
    for (let j = 0; j < line.length; j++) {
      const char = line[j];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        fields.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    fields.push(current.trim()); // Add last field
    
    if (fields.length < 3) continue; // Skip incomplete records
    
    const [codigo, sigla, dependencia, responsable, correo, recorrido, ext, direccion, profesional, enlaces] = fields;
    
    if (!codigo || !sigla || !dependencia) continue;
    
    totalRecords++;
    
    // Clean up fields
    const cleanCodigo = codigo.trim();
    const cleanSigla = sigla.trim();
    const cleanDependencia = dependencia.replace(/"/g, '').trim();
    
    // Store organizational data
    const orgData = {
      codigo: cleanCodigo,
      sigla: cleanSigla,
      nombre: cleanDependencia,
      responsable: responsable ? responsable.replace(/"/g, '').trim() : '',
      correo: correo ? correo.trim() : '',
      recorrido: recorrido ? recorrido.trim() : '',
      extension: ext ? ext.trim() : '',
      direccion: direccion ? direccion.replace(/"/g, '').trim() : '',
      profesional: profesional ? profesional.replace(/"/g, '').trim() : '',
      enlaces: enlaces ? enlaces.replace(/"/g, '').trim() : ''
    };
    
    // Count records with contact info
    if (orgData.correo || orgData.direccion || orgData.extension) {
      recordsWithContact++;
    }
    
    // Categorize by codigo pattern
    if (cleanCodigo.length === 3) {
      // Main dependencia (3-digit code)
      organizationalData.dependencias[cleanCodigo] = orgData;
    } else if (cleanCodigo.length === 3 && cleanCodigo !== '000') {
      // Could be subdependencia
      organizationalData.subdependencias[cleanCodigo] = orgData;
    }
    
    // Store all mappings
    organizationalData.codigoMapping[cleanCodigo] = orgData;
    organizationalData.siglaMapping[cleanSigla] = orgData;
    organizationalData.contactInfo[cleanCodigo] = {
      responsable: orgData.responsable,
      correo: orgData.correo,
      direccion: orgData.direccion,
      extension: orgData.extension,
      profesional: orgData.profesional,
      enlaces: orgData.enlaces
    };
  }
  
  console.log('\n=== ANALYSIS RESULTS ===');
  console.log(`Total valid records: ${totalRecords}`);
  console.log(`Records with contact info: ${recordsWithContact}`);
  console.log(`Main dependencias found: ${Object.keys(organizationalData.dependencias).length}`);
  console.log(`Total organizational units: ${Object.keys(organizationalData.codigoMapping).length}`);
  
  console.log('\n=== DEPENDENCIAS FOUND ===');
  Object.entries(organizationalData.dependencias).forEach(([codigo, data]) => {
    console.log(`${codigo}: ${data.sigla} - ${data.nombre}`);
  });
  
  console.log('\n=== SIGLA MAPPING SAMPLE ===');
  const siglaKeys = Object.keys(organizationalData.siglaMapping).slice(0, 10);
  siglaKeys.forEach(sigla => {
    const data = organizationalData.siglaMapping[sigla];
    console.log(`${sigla}: ${data.codigo} - ${data.nombre}`);
  });
  
  // Save analysis results
  const analysisReport = {
    timestamp: new Date().toISOString(),
    analysis_type: 'csv_directory_structure',
    total_records: totalRecords,
    records_with_contact: recordsWithContact,
    dependencias_count: Object.keys(organizationalData.dependencias).length,
    total_organizational_units: Object.keys(organizationalData.codigoMapping).length,
    organizational_data: organizationalData
  };
  
  fs.writeFileSync('csv_directory_analysis.json', JSON.stringify(analysisReport, null, 2));
  console.log('\n✅ Analysis saved to csv_directory_analysis.json');
  
  // Create simplified mapping for JSON enhancement
  const enhancementMapping = {
    codigo_to_data: organizationalData.codigoMapping,
    sigla_to_data: organizationalData.siglaMapping,
    contact_info: organizationalData.contactInfo
  };
  
  fs.writeFileSync('csv_enhancement_mapping.json', JSON.stringify(enhancementMapping, null, 2));
  console.log('✅ Enhancement mapping saved to csv_enhancement_mapping.json');
  
} catch (error) {
  console.error('❌ Error analyzing CSV:', error.message);
  process.exit(1);
}
