{"timestamp": "2025-07-08T15:34:38.693Z", "validation_type": "post_encoding_fix", "files_validated": [{"filename": "tramites_chia_optimo.json", "valid": true, "fileSize": 67903, "totalRecords": 0, "contactFields": 36, "spanishCharacters": 9, "remainingCorruption": 0, "spanishTests": [{"pattern": "Número (street numbers)", "count": 17}, {"pattern": "MARÍA (name)", "count": 1}, {"pattern": "CAST<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}]}, {"filename": "OPA-chia-optimo.json", "valid": true, "fileSize": 134354, "totalRecords": 0, "contactFields": 52, "spanishCharacters": 14, "remainingCorruption": 0, "spanishTests": [{"pattern": "Número (street numbers)", "count": 58}, {"pattern": "MARÍA (name)", "count": 2}, {"pattern": "CAST<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (surname)", "count": 2}, {"pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON> (surname)", "count": 2}, {"pattern": "<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "JOSÉ (name)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (surname)", "count": 4}, {"pattern": "<PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}, {"pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON> (surname)", "count": 1}]}], "summary": {"all_files_valid": true, "total_records": 0, "total_contact_fields": 88, "spanish_characters_found": 23, "remaining_corruption": 0}}