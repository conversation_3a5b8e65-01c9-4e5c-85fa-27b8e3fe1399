const fs = require('fs');

console.log('=== EXTRACTING HIERARCHICAL METADATA FROM OPA FILE ===\n');

try {
  console.log('Reading OPA file...');
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  console.log('✅ OPA file loaded successfully\n');

  const hierarchicalMetadata = {
    dependencias: {}
  };

  console.log('Extracting metadata structure...\n');

  // Extract dependencia and subdependencia metadata
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Processing DEPENDENCIA ${depCode}: ${depData.nombre} (${depData.sigla})`);

    hierarchicalMetadata.dependencias[depCode] = {
      nombre: depData.nombre,
      sigla: depData.sigla,
      subdependencias: {}
    };

    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        console.log(`  └─ SUBDEPENDENCIA ${subCode}: ${subData.nombre} (${subData.sigla})`);

        hierarchicalMetadata.dependencias[depCode].subdependencias[subCode] = {
          nombre: subData.nombre,
          sigla: subData.sigla
        };
      });
    }
    console.log('');
  });

  // Save the extracted metadata
  fs.writeFileSync('opa_hierarchical_metadata.json', JSON.stringify(hierarchicalMetadata, null, 2));
  console.log('✅ Hierarchical metadata saved to opa_hierarchical_metadata.json');

  // Display summary
  const totalDependencias = Object.keys(hierarchicalMetadata.dependencias).length;
  const totalSubdependencias = Object.values(hierarchicalMetadata.dependencias)
    .reduce((sum, dep) => sum + Object.keys(dep.subdependencias).length, 0);

  console.log(`\n=== METADATA SUMMARY ===`);
  console.log(`Total Dependencias: ${totalDependencias}`);
  console.log(`Total Subdependencias: ${totalSubdependencias}`);

  // Show sample structure
  console.log(`\n=== SAMPLE METADATA STRUCTURE ===`);
  const firstDepCode = Object.keys(hierarchicalMetadata.dependencias)[0];
  const firstDep = hierarchicalMetadata.dependencias[firstDepCode];
  const firstSubCode = Object.keys(firstDep.subdependencias)[0];
  const firstSub = firstDep.subdependencias[firstSubCode];

  console.log(`Sample structure:`);
  console.log(`{`);
  console.log(`  "dependencias": {`);
  console.log(`    "${firstDepCode}": {`);
  console.log(`      "nombre": "${firstDep.nombre}",`);
  console.log(`      "sigla": "${firstDep.sigla}",`);
  console.log(`      "subdependencias": {`);
  console.log(`        "${firstSubCode}": {`);
  console.log(`          "nombre": "${firstSub.nombre}",`);
  console.log(`          "sigla": "${firstSub.sigla}"`);
  console.log(`        }`);
  console.log(`      }`);
  console.log(`    }`);
  console.log(`  }`);
  console.log(`}`);

  console.log('\n✅ Metadata extraction complete');

} catch (error) {
  console.error('❌ Error during metadata extraction:', error.message);
  process.exit(1);
}
