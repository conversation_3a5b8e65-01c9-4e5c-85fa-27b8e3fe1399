const fs = require('fs');

console.log('=== CROSS-REFERENCING ORGANIZATIONAL METADATA ===\n');

try {
  // Load files
  console.log('Loading files...');
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  const csvMapping = JSON.parse(fs.readFileSync('csv_enhancement_mapping.json', 'utf8'));
  
  console.log('✅ All files loaded successfully');
  
  const discrepancies = {
    tramites: {
      dependencias: [],
      subdependencias: []
    },
    opa: {
      dependencias: [],
      subdependencias: []
    },
    missing_in_csv: [],
    sigla_mismatches: [],
    nombre_mismatches: []
  };
  
  const enhancements = {
    contact_info_additions: [],
    sigla_corrections: [],
    nombre_corrections: []
  };
  
  console.log('\n=== ANALYZING TRAMITES METADATA ===');
  
  // Analyze tramites dependencias and subdependencias
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Checking dependencia ${depCode}: ${depData.nombre} (${depData.sigla})`);
    
    // Check if dependencia exists in CSV
    const csvDepData = csvMapping.codigo_to_data[depCode];
    if (!csvDepData) {
      discrepancies.missing_in_csv.push({
        type: 'dependencia',
        source: 'tramites',
        codigo: depCode,
        current_data: depData
      });
      console.log(`  ❌ Not found in CSV directory`);
    } else {
      // Check sigla match
      if (depData.sigla !== csvDepData.sigla) {
        discrepancies.sigla_mismatches.push({
          type: 'dependencia',
          source: 'tramites',
          codigo: depCode,
          current_sigla: depData.sigla,
          csv_sigla: csvDepData.sigla
        });
        console.log(`  ⚠️  Sigla mismatch: ${depData.sigla} vs ${csvDepData.sigla}`);
      }
      
      // Check nombre similarity (basic check)
      const currentNombre = depData.nombre.toLowerCase().trim();
      const csvNombre = csvDepData.nombre.toLowerCase().trim();
      if (!currentNombre.includes(csvNombre.split(' ')[0]) && !csvNombre.includes(currentNombre.split(' ')[0])) {
        discrepancies.nombre_mismatches.push({
          type: 'dependencia',
          source: 'tramites',
          codigo: depCode,
          current_nombre: depData.nombre,
          csv_nombre: csvDepData.nombre
        });
        console.log(`  ⚠️  Nombre mismatch: ${depData.nombre} vs ${csvDepData.nombre}`);
      }
      
      // Check for contact info enhancement opportunities
      if (csvDepData.correo || csvDepData.direccion || csvDepData.extension) {
        enhancements.contact_info_additions.push({
          type: 'dependencia',
          source: 'tramites',
          codigo: depCode,
          contact_data: {
            correo: csvDepData.correo,
            direccion: csvDepData.direccion,
            extension: csvDepData.extension,
            responsable: csvDepData.responsable
          }
        });
        console.log(`  ✅ Contact info available for enhancement`);
      }
    }
    
    // Check subdependencias
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      console.log(`  Checking subdependencia ${subCode}: ${subData.nombre} (${subData.sigla})`);
      
      const csvSubData = csvMapping.codigo_to_data[subCode];
      if (!csvSubData) {
        discrepancies.missing_in_csv.push({
          type: 'subdependencia',
          source: 'tramites',
          codigo: subCode,
          parent_codigo: depCode,
          current_data: subData
        });
        console.log(`    ❌ Not found in CSV directory`);
      } else {
        // Similar checks for subdependencias
        if (subData.sigla !== csvSubData.sigla) {
          discrepancies.sigla_mismatches.push({
            type: 'subdependencia',
            source: 'tramites',
            codigo: subCode,
            parent_codigo: depCode,
            current_sigla: subData.sigla,
            csv_sigla: csvSubData.sigla
          });
          console.log(`    ⚠️  Sigla mismatch: ${subData.sigla} vs ${csvSubData.sigla}`);
        }
        
        if (csvSubData.correo || csvSubData.direccion || csvSubData.extension) {
          enhancements.contact_info_additions.push({
            type: 'subdependencia',
            source: 'tramites',
            codigo: subCode,
            parent_codigo: depCode,
            contact_data: {
              correo: csvSubData.correo,
              direccion: csvSubData.direccion,
              extension: csvSubData.extension,
              responsable: csvSubData.responsable
            }
          });
          console.log(`    ✅ Contact info available for enhancement`);
        }
      }
    });
  });
  
  console.log('\n=== ANALYZING OPA METADATA ===');
  
  // Similar analysis for OPA data
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Checking OPA dependencia ${depCode}: ${depData.nombre} (${depData.sigla})`);
    
    const csvDepData = csvMapping.codigo_to_data[depCode];
    if (!csvDepData) {
      discrepancies.missing_in_csv.push({
        type: 'dependencia',
        source: 'opa',
        codigo: depCode,
        current_data: depData
      });
      console.log(`  ❌ Not found in CSV directory`);
    } else {
      if (depData.sigla !== csvDepData.sigla) {
        discrepancies.sigla_mismatches.push({
          type: 'dependencia',
          source: 'opa',
          codigo: depCode,
          current_sigla: depData.sigla,
          csv_sigla: csvDepData.sigla
        });
        console.log(`  ⚠️  Sigla mismatch: ${depData.sigla} vs ${csvDepData.sigla}`);
      }
      
      if (csvDepData.correo || csvDepData.direccion || csvDepData.extension) {
        enhancements.contact_info_additions.push({
          type: 'dependencia',
          source: 'opa',
          codigo: depCode,
          contact_data: {
            correo: csvDepData.correo,
            direccion: csvDepData.direccion,
            extension: csvDepData.extension,
            responsable: csvDepData.responsable
          }
        });
        console.log(`  ✅ Contact info available for enhancement`);
      }
    }
    
    // Check OPA subdependencias
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      const csvSubData = csvMapping.codigo_to_data[subCode];
      if (csvSubData && (csvSubData.correo || csvSubData.direccion || csvSubData.extension)) {
        enhancements.contact_info_additions.push({
          type: 'subdependencia',
          source: 'opa',
          codigo: subCode,
          parent_codigo: depCode,
          contact_data: {
            correo: csvSubData.correo,
            direccion: csvSubData.direccion,
            extension: csvSubData.extension,
            responsable: csvSubData.responsable
          }
        });
      }
    });
  });
  
  console.log('\n=== CROSS-REFERENCE SUMMARY ===');
  console.log(`Missing in CSV: ${discrepancies.missing_in_csv.length}`);
  console.log(`Sigla mismatches: ${discrepancies.sigla_mismatches.length}`);
  console.log(`Nombre mismatches: ${discrepancies.nombre_mismatches.length}`);
  console.log(`Contact info enhancements available: ${enhancements.contact_info_additions.length}`);
  
  // Save cross-reference report
  const crossRefReport = {
    timestamp: new Date().toISOString(),
    analysis_type: 'metadata_cross_reference',
    discrepancies: discrepancies,
    enhancements: enhancements,
    summary: {
      missing_in_csv: discrepancies.missing_in_csv.length,
      sigla_mismatches: discrepancies.sigla_mismatches.length,
      nombre_mismatches: discrepancies.nombre_mismatches.length,
      contact_enhancements: enhancements.contact_info_additions.length
    }
  };
  
  fs.writeFileSync('metadata_cross_reference_report.json', JSON.stringify(crossRefReport, null, 2));
  console.log('\n✅ Cross-reference report saved to metadata_cross_reference_report.json');
  
} catch (error) {
  console.error('❌ Error during cross-reference:', error.message);
  process.exit(1);
}
