const fs = require('fs');

console.log('=== VALIDATING ENHANCED METADATA ===\n');

try {
  // Load files
  console.log('Loading files...');
  const tramitesData = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  const enhancementReport = JSON.parse(fs.readFileSync('metadata_enhancement_report.json', 'utf8'));
  
  console.log('✅ All files loaded successfully');
  
  const validation = {
    tramites: {
      total_tramites: 0,
      unique_codes: new Set(),
      dependencias_with_contact: 0,
      subdependencias_with_contact: 0,
      contact_fields_found: 0
    },
    opa: {
      total_opas: 0,
      unique_codes: new Set(),
      dependencias_with_contact: 0,
      subdependencias_with_contact: 0,
      contact_fields_found: 0
    },
    structure_integrity: true,
    issues: []
  };
  
  console.log('\n=== VALIDATING TRAMITES STRUCTURE AND METADATA ===');
  
  // Validate tramites
  Object.entries(tramitesData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Validating dependencia ${depCode}: ${depData.nombre}`);
    
    // Check for contact information
    const contactFields = ['correo', 'direccion', 'extension', 'responsable'];
    const hasContact = contactFields.some(field => depData[field]);
    if (hasContact) {
      validation.tramites.dependencias_with_contact++;
      contactFields.forEach(field => {
        if (depData[field]) validation.tramites.contact_fields_found++;
      });
      console.log(`  ✅ Contact info: ${contactFields.filter(f => depData[f]).join(', ')}`);
    }
    
    // Validate subdependencias
    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        console.log(`  Validating subdependencia ${subCode}: ${subData.nombre}`);
        
        // Check for contact information
        const hasSubContact = contactFields.some(field => subData[field]);
        if (hasSubContact) {
          validation.tramites.subdependencias_with_contact++;
          contactFields.forEach(field => {
            if (subData[field]) validation.tramites.contact_fields_found++;
          });
          console.log(`    ✅ Contact info: ${contactFields.filter(f => subData[f]).join(', ')}`);
        }
        
        // Validate tramites
        if (subData.tramites && Array.isArray(subData.tramites)) {
          subData.tramites.forEach(tramite => {
            validation.tramites.total_tramites++;
            if (tramite.codigo_unico) {
              validation.tramites.unique_codes.add(tramite.codigo_unico);
            }
            
            // Validate required fields
            const requiredFields = ['codigo_unico', 'nombre', 'formulario', 'tiempo_respuesta', 'tiene_pago'];
            requiredFields.forEach(field => {
              if (!tramite[field]) {
                validation.issues.push(`Tramite missing field ${field}: ${tramite.codigo_unico || 'unknown'}`);
                validation.structure_integrity = false;
              }
            });
          });
        }
      });
    }
  });
  
  console.log('\n=== VALIDATING OPA STRUCTURE AND METADATA ===');
  
  // Validate OPA
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Validating OPA dependencia ${depCode}: ${depData.nombre}`);
    
    // Check for contact information
    const contactFields = ['correo', 'direccion', 'extension', 'responsable'];
    const hasContact = contactFields.some(field => depData[field]);
    if (hasContact) {
      validation.opa.dependencias_with_contact++;
      contactFields.forEach(field => {
        if (depData[field]) validation.opa.contact_fields_found++;
      });
      console.log(`  ✅ Contact info: ${contactFields.filter(f => depData[f]).join(', ')}`);
    }
    
    // Validate subdependencias
    if (depData.subdependencias) {
      Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
        const hasSubContact = contactFields.some(field => subData[field]);
        if (hasSubContact) {
          validation.opa.subdependencias_with_contact++;
          contactFields.forEach(field => {
            if (subData[field]) validation.opa.contact_fields_found++;
          });
        }
        
        // Validate OPAs
        if (subData.OPA && Array.isArray(subData.OPA)) {
          subData.OPA.forEach(opa => {
            validation.opa.total_opas++;
            if (opa.codigo_OPA) {
              validation.opa.unique_codes.add(opa.codigo_OPA);
            }
          });
        }
      });
    }
  });
  
  console.log('\n=== VALIDATION RESULTS ===');
  
  console.log('TRAMITES:');
  console.log(`  Total tramites: ${validation.tramites.total_tramites}`);
  console.log(`  Unique codes: ${validation.tramites.unique_codes.size}`);
  console.log(`  Dependencias with contact: ${validation.tramites.dependencias_with_contact}`);
  console.log(`  Subdependencias with contact: ${validation.tramites.subdependencias_with_contact}`);
  console.log(`  Contact fields found: ${validation.tramites.contact_fields_found}`);
  
  console.log('OPA:');
  console.log(`  Total OPAs: ${validation.opa.total_opas}`);
  console.log(`  Unique codes: ${validation.opa.unique_codes.size}`);
  console.log(`  Dependencias with contact: ${validation.opa.dependencias_with_contact}`);
  console.log(`  Subdependencias with contact: ${validation.opa.subdependencias_with_contact}`);
  console.log(`  Contact fields found: ${validation.opa.contact_fields_found}`);
  
  console.log(`Structure integrity: ${validation.structure_integrity ? '✅' : '❌'}`);
  
  if (validation.issues.length > 0) {
    console.log('\n❌ ISSUES FOUND:');
    validation.issues.forEach(issue => console.log(`  - ${issue}`));
  } else {
    console.log('\n✅ NO STRUCTURAL ISSUES FOUND');
  }
  
  // Compare with enhancement report
  console.log('\n=== ENHANCEMENT VERIFICATION ===');
  const expectedTramitesContact = enhancementReport.enhancement_log.tramites.contact_fields_added;
  const expectedOpaContact = enhancementReport.enhancement_log.opa.contact_fields_added;
  
  console.log(`Expected tramites contact fields: ${expectedTramitesContact}`);
  console.log(`Found tramites contact fields: ${validation.tramites.contact_fields_found}`);
  console.log(`Expected OPA contact fields: ${expectedOpaContact}`);
  console.log(`Found OPA contact fields: ${validation.opa.contact_fields_found}`);
  
  const enhancementVerified = (
    validation.tramites.contact_fields_found === expectedTramitesContact &&
    validation.opa.contact_fields_found === expectedOpaContact
  );
  
  console.log(`Enhancement verification: ${enhancementVerified ? '✅' : '❌'}`);
  
  // Save validation report
  const validationReport = {
    timestamp: new Date().toISOString(),
    validation_type: 'enhanced_metadata_validation',
    validation_results: validation,
    enhancement_verified: enhancementVerified,
    overall_success: validation.structure_integrity && enhancementVerified && validation.issues.length === 0
  };
  
  fs.writeFileSync('enhanced_metadata_validation.json', JSON.stringify(validationReport, null, 2));
  console.log('\n✅ Validation report saved to enhanced_metadata_validation.json');
  
  if (validationReport.overall_success) {
    console.log('\n🎉 VALIDATION PASSED - Enhanced metadata is valid and complete!');
  } else {
    console.log('\n❌ VALIDATION FAILED - Review issues before proceeding');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Error during validation:', error.message);
  process.exit(1);
}
