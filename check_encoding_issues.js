const fs = require('fs');
const path = require('path');

console.log('=== CHECKING ENCODING ISSUES IN JSON FILES ===\n');

// Function to detect potential encoding issues
function detectEncodingIssues(text, filename) {
  const issues = [];
  
  // Common encoding corruption patterns for Spanish characters
  const corruptionPatterns = [
    { pattern: /N�mero/g, correct: 'Número', description: 'ú corrupted to �' },
    { pattern: /�/g, correct: '?', description: 'Generic replacement character found' },
    { pattern: /Ã¡/g, correct: 'á', description: 'á double-encoded' },
    { pattern: /Ã©/g, correct: 'é', description: 'é double-encoded' },
    { pattern: /Ã­/g, correct: 'í', description: 'í double-encoded' },
    { pattern: /Ã³/g, correct: 'ó', description: 'ó double-encoded' },
    { pattern: /Ãº/g, correct: 'ú', description: 'ú double-encoded' },
    { pattern: /Ã±/g, correct: 'ñ', description: 'ñ double-encoded' },
    { pattern: /Ã¼/g, correct: 'ü', description: 'ü double-encoded' },
    { pattern: /Ã/g, correct: 'Á', description: 'Á double-encoded' },
    { pattern: /Ã‰/g, correct: 'É', description: 'É double-encoded' },
    { pattern: /Ã/g, correct: 'Í', description: 'Í double-encoded' },
    { pattern: /Ã"/g, correct: 'Ó', description: 'Ó double-encoded' },
    { pattern: /Ãš/g, correct: 'Ú', description: 'Ú double-encoded' },
    { pattern: /Ã'/g, correct: 'Ñ', description: 'Ñ double-encoded' }
  ];
  
  // Check for each corruption pattern
  corruptionPatterns.forEach(({ pattern, correct, description }) => {
    const matches = text.match(pattern);
    if (matches) {
      issues.push({
        pattern: pattern.source,
        correct: correct,
        description: description,
        count: matches.length,
        filename: filename
      });
    }
  });
  
  return issues;
}

// Function to get file encoding info
function getFileInfo(filepath) {
  try {
    const stats = fs.statSync(filepath);
    const buffer = fs.readFileSync(filepath);
    
    // Check for BOM (Byte Order Mark)
    let hasBOM = false;
    let bomType = 'none';
    
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
      hasBOM = true;
      bomType = 'UTF-8';
    } else if (buffer.length >= 2 && buffer[0] === 0xFF && buffer[1] === 0xFE) {
      hasBOM = true;
      bomType = 'UTF-16LE';
    } else if (buffer.length >= 2 && buffer[0] === 0xFE && buffer[1] === 0xFF) {
      hasBOM = true;
      bomType = 'UTF-16BE';
    }
    
    return {
      size: stats.size,
      hasBOM: hasBOM,
      bomType: bomType,
      firstBytes: Array.from(buffer.slice(0, 10)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')
    };
  } catch (error) {
    return { error: error.message };
  }
}

try {
  const files = ['tramites_chia_optimo.json', 'OPA-chia-optimo.json'];
  const allIssues = [];
  
  console.log('=== FILE ENCODING ANALYSIS ===');
  
  files.forEach(filename => {
    console.log(`\nAnalyzing: ${filename}`);
    
    // Get file info
    const fileInfo = getFileInfo(filename);
    console.log(`File size: ${fileInfo.size} bytes`);
    console.log(`BOM detected: ${fileInfo.hasBOM} (${fileInfo.bomType})`);
    console.log(`First bytes: ${fileInfo.firstBytes}`);
    
    // Read file content
    const content = fs.readFileSync(filename, 'utf8');
    
    // Check for encoding issues
    const issues = detectEncodingIssues(content, filename);
    
    if (issues.length > 0) {
      console.log(`❌ Found ${issues.length} encoding issue types:`);
      issues.forEach(issue => {
        console.log(`  - ${issue.description}: ${issue.count} occurrences`);
        console.log(`    Pattern: ${issue.pattern} → Should be: ${issue.correct}`);
      });
      allIssues.push(...issues);
    } else {
      console.log('✅ No obvious encoding corruption detected');
    }
    
    // Check for specific problematic characters in contact fields
    const contactFieldMatches = content.match(/"(correo|direccion|responsable)":\s*"[^"]*"/g);
    if (contactFieldMatches) {
      console.log(`Contact fields found: ${contactFieldMatches.length}`);
      
      // Look for suspicious characters in contact fields
      const suspiciousInContact = contactFieldMatches.filter(field => 
        field.includes('�') || field.includes('Ã') || field.includes('N�mero')
      );
      
      if (suspiciousInContact.length > 0) {
        console.log(`❌ Suspicious characters in ${suspiciousInContact.length} contact fields:`);
        suspiciousInContact.slice(0, 5).forEach(field => {
          console.log(`    ${field.substring(0, 80)}...`);
        });
      }
    }
  });
  
  console.log('\n=== OVERALL ENCODING SUMMARY ===');
  if (allIssues.length > 0) {
    console.log(`❌ Total encoding issues found: ${allIssues.length} types`);
    
    // Group by issue type
    const issuesByType = {};
    allIssues.forEach(issue => {
      const key = issue.description;
      if (!issuesByType[key]) {
        issuesByType[key] = { count: 0, files: new Set() };
      }
      issuesByType[key].count += issue.count;
      issuesByType[key].files.add(issue.filename);
    });
    
    console.log('\nIssue breakdown:');
    Object.entries(issuesByType).forEach(([type, data]) => {
      console.log(`  ${type}: ${data.count} occurrences in ${Array.from(data.files).join(', ')}`);
    });
  } else {
    console.log('✅ No encoding issues detected in either file');
  }
  
  // Save detailed analysis
  const encodingAnalysis = {
    timestamp: new Date().toISOString(),
    analysis_type: 'encoding_validation',
    files_analyzed: files.map(filename => ({
      filename: filename,
      file_info: getFileInfo(filename),
      issues: detectEncodingIssues(fs.readFileSync(filename, 'utf8'), filename)
    })),
    total_issues: allIssues.length,
    issues_by_type: allIssues.reduce((acc, issue) => {
      const key = issue.description;
      if (!acc[key]) acc[key] = [];
      acc[key].push(issue);
      return acc;
    }, {})
  };
  
  fs.writeFileSync('encoding_analysis_report.json', JSON.stringify(encodingAnalysis, null, 2));
  console.log('\n✅ Detailed analysis saved to encoding_analysis_report.json');
  
} catch (error) {
  console.error('❌ Error during encoding analysis:', error.message);
  process.exit(1);
}
