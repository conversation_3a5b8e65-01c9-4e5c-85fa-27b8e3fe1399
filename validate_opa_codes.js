const fs = require('fs');

console.log('=== VALIDATING OPA CODES ===\n');

try {
  console.log('Reading updated OPA file...');
  const opaData = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));
  console.log('✅ OPA file loaded successfully\n');

  let totalValidated = 0;
  let validCodes = 0;
  let invalidCodes = 0;
  const allCodes = new Set();
  const duplicateCodes = [];
  const invalidFormatCodes = [];
  const validationResults = [];

  // XXX-YYY-ZZZ format regex
  const codeFormatRegex = /^\d{3}-\d{3}-\d{3}$/;

  console.log('Validating all OPA codes...\n');

  // Validate each dependencia
  Object.entries(opaData.dependencias).forEach(([depCode, depData]) => {
    console.log(`Validating DEPENDENCIA ${depCode}: ${depData.nombre}`);
    
    // Validate each subdependencia
    Object.entries(depData.subdependencias).forEach(([subCode, subData]) => {
      const opaCount = subData.OPA ? subData.OPA.length : 0;
      console.log(`  └─ SUBDEPENDENCIA ${subCode}: ${subData.nombre} - ${opaCount} OPAs`);
      
      if (subData.OPA && subData.OPA.length > 0) {
        subData.OPA.forEach((opa, index) => {
          const code = opa.codigo_OPA;
          totalValidated++;
          
          // Check format
          const isValidFormat = codeFormatRegex.test(code);
          if (!isValidFormat) {
            invalidCodes++;
            invalidFormatCodes.push({
              code: code,
              location: `${depCode}-${subCode}`,
              index: index,
              expected: `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${(index + 1).toString().padStart(3, '0')}`
            });
          } else {
            validCodes++;
          }
          
          // Check uniqueness
          if (allCodes.has(code)) {
            duplicateCodes.push({
              code: code,
              location: `${depCode}-${subCode}`,
              index: index
            });
          } else {
            allCodes.add(code);
          }
          
          // Check if code matches expected pattern
          const expectedCode = `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${(index + 1).toString().padStart(3, '0')}`;
          const isCorrectCode = code === expectedCode;
          
          validationResults.push({
            dependencia: depCode,
            subdependencia: subCode,
            index: index + 1,
            currentCode: code,
            expectedCode: expectedCode,
            isValidFormat: isValidFormat,
            isCorrectCode: isCorrectCode,
            opaText: opa.OPA.substring(0, 50) + (opa.OPA.length > 50 ? '...' : '')
          });
        });
        
        // Show validation status for this subdependencia
        const firstCode = subData.OPA[0].codigo_OPA;
        const lastCode = subData.OPA[subData.OPA.length - 1].codigo_OPA;
        const expectedFirst = `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-001`;
        const expectedLast = `${depCode.padStart(3, '0')}-${subCode.padStart(3, '0')}-${opaCount.toString().padStart(3, '0')}`;
        
        if (firstCode === expectedFirst && lastCode === expectedLast) {
          console.log(`      ✅ Codes valid: ${firstCode} to ${lastCode}`);
        } else {
          console.log(`      ❌ Code mismatch: Got ${firstCode}-${lastCode}, Expected ${expectedFirst}-${expectedLast}`);
        }
      }
    });
    console.log('');
  });

  console.log(`=== VALIDATION SUMMARY ===`);
  console.log(`Total OPAs validated: ${totalValidated}`);
  console.log(`Valid format codes: ${validCodes}`);
  console.log(`Invalid format codes: ${invalidCodes}`);
  console.log(`Unique codes: ${allCodes.size}`);
  console.log(`Duplicate codes: ${duplicateCodes.length}`);

  // Report issues
  if (invalidFormatCodes.length > 0) {
    console.log(`\n❌ INVALID FORMAT CODES (${invalidFormatCodes.length}):`);
    invalidFormatCodes.slice(0, 10).forEach(item => {
      console.log(`  ${item.code} at ${item.location} (expected: ${item.expected})`);
    });
    if (invalidFormatCodes.length > 10) {
      console.log(`  ... and ${invalidFormatCodes.length - 10} more`);
    }
  }

  if (duplicateCodes.length > 0) {
    console.log(`\n❌ DUPLICATE CODES (${duplicateCodes.length}):`);
    duplicateCodes.slice(0, 10).forEach(item => {
      console.log(`  ${item.code} at ${item.location}`);
    });
    if (duplicateCodes.length > 10) {
      console.log(`  ... and ${duplicateCodes.length - 10} more`);
    }
  }

  // Overall validation result
  const isValid = invalidCodes === 0 && duplicateCodes.length === 0 && allCodes.size === totalValidated;
  
  if (isValid) {
    console.log('\n✅ VALIDATION PASSED');
    console.log('✅ All OPA codes are properly formatted and unique');
    console.log(`✅ ${totalValidated} codes follow XXX-YYY-ZZZ format`);
  } else {
    console.log('\n❌ VALIDATION FAILED');
    console.log('❌ Issues found in OPA code standardization');
  }

  // Save validation report
  const validationReport = {
    timestamp: new Date().toISOString(),
    summary: {
      totalValidated: totalValidated,
      validCodes: validCodes,
      invalidCodes: invalidCodes,
      uniqueCodes: allCodes.size,
      duplicateCodes: duplicateCodes.length,
      validationPassed: isValid
    },
    issues: {
      invalidFormatCodes: invalidFormatCodes,
      duplicateCodes: duplicateCodes
    },
    validationResults: validationResults
  };

  fs.writeFileSync('opa_validation_report.json', JSON.stringify(validationReport, null, 2));
  console.log('\n✅ Validation report saved to opa_validation_report.json');

  // Show sample of valid codes
  console.log('\n=== SAMPLE VALID CODES ===');
  validationResults
    .filter(r => r.isValidFormat && r.isCorrectCode)
    .slice(0, 10)
    .forEach(r => {
      console.log(`${r.currentCode} | ${r.opaText}`);
    });

  process.exit(isValid ? 0 : 1);

} catch (error) {
  console.error('❌ Error during validation:', error.message);
  process.exit(1);
}
