{"timestamp": "2025-07-08T15:28:26.186Z", "analysis_type": "encoding_validation", "files_analyzed": [{"filename": "tramites_chia_optimo.json", "file_info": {"size": 68478, "hasBOM": false, "bomType": "none", "firstBytes": "0x7b 0x0a 0x20 0x20 0x22 0x64 0x65 0x70 0x65 0x6e"}, "issues": [{"pattern": "N�mero", "correct": "Número", "description": "ú corrupted to �", "count": 17, "filename": "tramites_chia_optimo.json"}, {"pattern": "�", "correct": "?", "description": "Generic replacement character found", "count": 22, "filename": "tramites_chia_optimo.json"}]}, {"filename": "OPA-chia-optimo.json", "file_info": {"size": 135173, "hasBOM": false, "bomType": "none", "firstBytes": "0x7b 0x0a 0x20 0x20 0x22 0x64 0x65 0x70 0x65 0x6e"}, "issues": [{"pattern": "N�mero", "correct": "Número", "description": "ú corrupted to �", "count": 58, "filename": "OPA-chia-optimo.json"}, {"pattern": "�", "correct": "?", "description": "Generic replacement character found", "count": 78, "filename": "OPA-chia-optimo.json"}]}], "total_issues": 4, "issues_by_type": {"ú corrupted to �": [{"pattern": "N�mero", "correct": "Número", "description": "ú corrupted to �", "count": 17, "filename": "tramites_chia_optimo.json"}, {"pattern": "N�mero", "correct": "Número", "description": "ú corrupted to �", "count": 58, "filename": "OPA-chia-optimo.json"}], "Generic replacement character found": [{"pattern": "�", "correct": "?", "description": "Generic replacement character found", "count": 22, "filename": "tramites_chia_optimo.json"}, {"pattern": "�", "correct": "?", "description": "Generic replacement character found", "count": 78, "filename": "OPA-chia-optimo.json"}]}}